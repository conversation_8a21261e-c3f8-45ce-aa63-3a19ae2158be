"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/WalletDashboard.tsx":
/*!******************************************************!*\
  !*** ./src/components/dashboard/WalletDashboard.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WalletDashboard: () => (/* binding */ WalletDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_layout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout */ \"(app-pages-browser)/./src/components/layout/index.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowDownLeft_ArrowUpRight_CheckCircle_Clock_Copy_Filter_RefreshCw_Search_Shield_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowDownLeft,ArrowUpRight,CheckCircle,Clock,Copy,Filter,RefreshCw,Search,Shield,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowDownLeft_ArrowUpRight_CheckCircle_Clock_Copy_Filter_RefreshCw_Search_Shield_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowDownLeft,ArrowUpRight,CheckCircle,Clock,Copy,Filter,RefreshCw,Search,Shield,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-down-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowDownLeft_ArrowUpRight_CheckCircle_Clock_Copy_Filter_RefreshCw_Search_Shield_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowDownLeft,ArrowUpRight,CheckCircle,Clock,Copy,Filter,RefreshCw,Search,Shield,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wallet.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowDownLeft_ArrowUpRight_CheckCircle_Clock_Copy_Filter_RefreshCw_Search_Shield_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowDownLeft,ArrowUpRight,CheckCircle,Clock,Copy,Filter,RefreshCw,Search,Shield,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowDownLeft_ArrowUpRight_CheckCircle_Clock_Copy_Filter_RefreshCw_Search_Shield_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowDownLeft,ArrowUpRight,CheckCircle,Clock,Copy,Filter,RefreshCw,Search,Shield,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowDownLeft_ArrowUpRight_CheckCircle_Clock_Copy_Filter_RefreshCw_Search_Shield_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowDownLeft,ArrowUpRight,CheckCircle,Clock,Copy,Filter,RefreshCw,Search,Shield,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowDownLeft_ArrowUpRight_CheckCircle_Clock_Copy_Filter_RefreshCw_Search_Shield_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowDownLeft,ArrowUpRight,CheckCircle,Clock,Copy,Filter,RefreshCw,Search,Shield,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowDownLeft_ArrowUpRight_CheckCircle_Clock_Copy_Filter_RefreshCw_Search_Shield_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowDownLeft,ArrowUpRight,CheckCircle,Clock,Copy,Filter,RefreshCw,Search,Shield,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowDownLeft_ArrowUpRight_CheckCircle_Clock_Copy_Filter_RefreshCw_Search_Shield_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowDownLeft,ArrowUpRight,CheckCircle,Clock,Copy,Filter,RefreshCw,Search,Shield,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowDownLeft_ArrowUpRight_CheckCircle_Clock_Copy_Filter_RefreshCw_Search_Shield_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowDownLeft,ArrowUpRight,CheckCircle,Clock,Copy,Filter,RefreshCw,Search,Shield,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowDownLeft_ArrowUpRight_CheckCircle_Clock_Copy_Filter_RefreshCw_Search_Shield_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowDownLeft,ArrowUpRight,CheckCircle,Clock,Copy,Filter,RefreshCw,Search,Shield,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowDownLeft_ArrowUpRight_CheckCircle_Clock_Copy_Filter_RefreshCw_Search_Shield_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowDownLeft,ArrowUpRight,CheckCircle,Clock,Copy,Filter,RefreshCw,Search,Shield,Wallet,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_wallet_DepositPage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/wallet/DepositPage */ \"(app-pages-browser)/./src/components/wallet/DepositPage.tsx\");\n/* __next_internal_client_entry_do_not_use__ WalletDashboard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst WalletDashboard = ()=>{\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    const [walletData, setWalletData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [withdrawalSettings, setWithdrawalSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showWithdrawModal, setShowWithdrawModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [withdrawalForm, setWithdrawalForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        amount: '',\n        usdtAddress: ''\n    });\n    const [userWithdrawalAddress, setUserWithdrawalAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [withdrawalLoading, setWithdrawalLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [withdrawalError, setWithdrawalError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Dialog hooks\n    const { showConfirm, hideConfirm, ConfirmDialog } = (0,_components_ui__WEBPACK_IMPORTED_MODULE_2__.useConfirmDialog)();\n    const { showMessage, hideMessage, MessageBoxComponent } = (0,_components_ui__WEBPACK_IMPORTED_MODULE_2__.useMessageBox)();\n    // Transaction filtering states\n    const [allTransactions, setAllTransactions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [transactionLoading, setTransactionLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [filterType, setFilterType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('ALL');\n    const [filterStatus, setFilterStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('ALL');\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [transactionTypes, setTransactionTypes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [statusOptions, setStatusOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Transaction detail modal states\n    const [selectedTransaction, setSelectedTransaction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showTransactionModal, setShowTransactionModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WalletDashboard.useEffect\": ()=>{\n            fetchWalletData();\n            fetchWithdrawalSettings();\n            fetchAllTransactions();\n            fetchUserWithdrawalAddress();\n            // Set up automatic refresh every 30 seconds (background only)\n            const refreshInterval = setInterval({\n                \"WalletDashboard.useEffect.refreshInterval\": ()=>{\n                    // Only refresh data in background without affecting loading states\n                    fetchWalletDataSilent();\n                    fetchAllTransactionsSilent();\n                }\n            }[\"WalletDashboard.useEffect.refreshInterval\"], 30000);\n            return ({\n                \"WalletDashboard.useEffect\": ()=>{\n                    clearInterval(refreshInterval);\n                }\n            })[\"WalletDashboard.useEffect\"];\n        }\n    }[\"WalletDashboard.useEffect\"], []);\n    // Fetch transactions when filters change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WalletDashboard.useEffect\": ()=>{\n            fetchAllTransactions();\n        }\n    }[\"WalletDashboard.useEffect\"], [\n        searchTerm,\n        filterType,\n        filterStatus\n    ]);\n    const fetchWalletData = async ()=>{\n        try {\n            const response = await fetch('/api/wallet/balance', {\n                credentials: 'include'\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success) {\n                    setWalletData(data.data);\n                }\n            }\n        } catch (error) {\n            console.error('Failed to fetch wallet data:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Silent fetch for background updates (no loading state changes)\n    const fetchWalletDataSilent = async ()=>{\n        try {\n            const response = await fetch('/api/wallet/balance', {\n                credentials: 'include'\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success) {\n                    setWalletData(data.data);\n                }\n            }\n        } catch (error) {\n            console.error('Failed to fetch wallet data silently:', error);\n        }\n    };\n    const fetchAllTransactions = async ()=>{\n        try {\n            setTransactionLoading(true);\n            const params = new URLSearchParams();\n            params.append('limit', '50');\n            if (searchTerm) params.append('search', searchTerm);\n            if (filterType !== 'ALL') params.append('type', filterType);\n            if (filterStatus !== 'ALL') params.append('status', filterStatus);\n            const response = await fetch(\"/api/wallet/transactions?\".concat(params), {\n                credentials: 'include'\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success) {\n                    setAllTransactions(data.data.transactions);\n                    setTransactionTypes(data.data.filters.transactionTypes);\n                    setStatusOptions(data.data.filters.statusOptions);\n                }\n            }\n        } catch (error) {\n            console.error('Failed to fetch transactions:', error);\n        } finally{\n            setTransactionLoading(false);\n        }\n    };\n    // Silent fetch for background updates (no loading state changes)\n    const fetchAllTransactionsSilent = async ()=>{\n        try {\n            const params = new URLSearchParams();\n            params.append('limit', '50');\n            if (searchTerm) params.append('search', searchTerm);\n            if (filterType !== 'ALL') params.append('type', filterType);\n            if (filterStatus !== 'ALL') params.append('status', filterStatus);\n            const response = await fetch(\"/api/wallet/transactions?\".concat(params), {\n                credentials: 'include'\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success) {\n                    setAllTransactions(data.data.transactions);\n                    setTransactionTypes(data.data.filters.transactionTypes);\n                    setStatusOptions(data.data.filters.statusOptions);\n                }\n            }\n        } catch (error) {\n            console.error('Failed to fetch transactions silently:', error);\n        }\n    };\n    const handleTransactionClick = (transaction)=>{\n        setSelectedTransaction(transaction);\n        setShowTransactionModal(true);\n    };\n    const fetchWithdrawalSettings = async ()=>{\n        try {\n            const response = await fetch('/api/wallet/withdrawal-settings', {\n                credentials: 'include'\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success) {\n                    setWithdrawalSettings(data.data);\n                }\n            }\n        } catch (error) {\n            console.error('Failed to fetch withdrawal settings:', error);\n        }\n    };\n    const fetchUserWithdrawalAddress = async ()=>{\n        try {\n            const response = await fetch('/api/user/withdrawal-address', {\n                credentials: 'include'\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success && data.data.withdrawalAddress) {\n                    setUserWithdrawalAddress(data.data.withdrawalAddress);\n                    // Auto-fill the withdrawal form if address is not already set\n                    if (!withdrawalForm.usdtAddress) {\n                        setWithdrawalForm((prev)=>({\n                                ...prev,\n                                usdtAddress: data.data.withdrawalAddress\n                            }));\n                    }\n                }\n            }\n        } catch (error) {\n            console.error('Failed to fetch user withdrawal address:', error);\n        }\n    };\n    const handleWithdrawalSubmit = async (e)=>{\n        e.preventDefault();\n        setWithdrawalError('');\n        try {\n            const amount = parseFloat(withdrawalForm.amount);\n            if (!amount || amount <= 0) {\n                throw new Error('Please enter a valid amount');\n            }\n            if (!withdrawalForm.usdtAddress) {\n                throw new Error('Please enter a USDT address');\n            }\n            // Calculate fees for confirmation\n            const fixedFee = (withdrawalSettings === null || withdrawalSettings === void 0 ? void 0 : withdrawalSettings.fixedFee) || 3;\n            const percentageFee = amount * ((withdrawalSettings === null || withdrawalSettings === void 0 ? void 0 : withdrawalSettings.percentageFee) || 1) / 100;\n            const totalFees = fixedFee + percentageFee;\n            const youReceive = amount - totalFees;\n            // Show confirmation dialog\n            showConfirm({\n                title: 'Confirm Withdrawal',\n                message: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-4 rounded-lg space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Withdrawal Amount:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-semibold\",\n                                            children: [\n                                                \"$\",\n                                                amount.toFixed(2),\n                                                \" USDT\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Fixed Fee:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-600\",\n                                            children: [\n                                                \"-$\",\n                                                fixedFee.toFixed(2),\n                                                \" USDT\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Percentage Fee (1%):\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-600\",\n                                            children: [\n                                                \"-$\",\n                                                percentageFee.toFixed(2),\n                                                \" USDT\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t pt-2 flex justify-between font-semibold\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-green-600\",\n                                            children: \"You Receive:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-green-600\",\n                                            children: [\n                                                \"$\",\n                                                youReceive.toFixed(2),\n                                                \" USDT\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-50 p-3 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-blue-800\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"USDT Address:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 47\n                                    }, undefined),\n                                    withdrawalForm.usdtAddress\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-yellow-50 p-3 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-yellow-800\",\n                                children: \"⚠️ Please double-check your address. Transactions cannot be reversed.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 11\n                }, undefined),\n                confirmText: 'Confirm Withdrawal',\n                cancelText: 'Cancel',\n                variant: 'warning',\n                onConfirm: ()=>processWithdrawal(amount)\n            });\n        } catch (err) {\n            setWithdrawalError(err.message || 'Invalid withdrawal details');\n        }\n    };\n    const processWithdrawal = async (amount)=>{\n        setWithdrawalLoading(true);\n        try {\n            const response = await fetch('/api/wallet/withdraw', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                credentials: 'include',\n                body: JSON.stringify({\n                    amount,\n                    usdtAddress: withdrawalForm.usdtAddress\n                })\n            });\n            const data = await response.json();\n            if (!data.success) {\n                throw new Error(data.error || 'Withdrawal failed');\n            }\n            // Show success message\n            showMessage({\n                title: 'Withdrawal Submitted',\n                message: 'Your withdrawal request has been submitted successfully. It will be processed within 3 business days.',\n                variant: 'success',\n                buttonText: 'OK'\n            });\n            // Reset form and close modal\n            setWithdrawalForm({\n                amount: '',\n                usdtAddress: ''\n            });\n            setActiveTab('overview');\n            // Refresh data\n            fetchWalletData();\n            fetchAllTransactions();\n        } catch (err) {\n            showMessage({\n                title: 'Withdrawal Failed',\n                message: err.message || 'Failed to process withdrawal. Please try again.',\n                variant: 'error',\n                buttonText: 'OK'\n            });\n        } finally{\n            setWithdrawalLoading(false);\n        }\n    };\n    const getTransactionIcon = (type)=>{\n        switch(type){\n            case 'WITHDRAWAL':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowDownLeft_ArrowUpRight_CheckCircle_Clock_Copy_Filter_RefreshCw_Search_Shield_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                    lineNumber: 366,\n                    columnNumber: 16\n                }, undefined);\n            case 'DEPOSIT':\n            case 'MINING_EARNINGS':\n            case 'DIRECT_REFERRAL':\n            case 'BINARY_BONUS':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowDownLeft_ArrowUpRight_CheckCircle_Clock_Copy_Filter_RefreshCw_Search_Shield_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4 text-eco-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                    lineNumber: 371,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowDownLeft_ArrowUpRight_CheckCircle_Clock_Copy_Filter_RefreshCw_Search_Shield_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                    lineNumber: 373,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const getTransactionColor = (type)=>{\n        switch(type){\n            case 'WITHDRAWAL':\n            case 'PURCHASE':\n                return 'text-red-600';\n            case 'DEPOSIT':\n            case 'MINING_EARNINGS':\n            case 'DIRECT_REFERRAL':\n            case 'BINARY_BONUS':\n                return 'text-eco-600';\n            default:\n                return 'text-gray-600';\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'COMPLETED':\n            case 'APPROVED':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowDownLeft_ArrowUpRight_CheckCircle_Clock_Copy_Filter_RefreshCw_Search_Shield_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4 text-eco-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                    lineNumber: 396,\n                    columnNumber: 16\n                }, undefined);\n            case 'PENDING':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowDownLeft_ArrowUpRight_CheckCircle_Clock_Copy_Filter_RefreshCw_Search_Shield_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-4 w-4 text-solar-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                    lineNumber: 398,\n                    columnNumber: 16\n                }, undefined);\n            case 'FAILED':\n            case 'REJECTED':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowDownLeft_ArrowUpRight_CheckCircle_Clock_Copy_Filter_RefreshCw_Search_Shield_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-4 w-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                    lineNumber: 401,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowDownLeft_ArrowUpRight_CheckCircle_Clock_Copy_Filter_RefreshCw_Search_Shield_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                    lineNumber: 403,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const calculateWithdrawalFees = (amount)=>{\n        if (!withdrawalSettings || !amount) {\n            return {\n                fixedFee: 0,\n                percentageFee: 0,\n                totalFees: 0,\n                totalDeduction: 0,\n                netAmount: 0\n            };\n        }\n        const fixedFee = withdrawalSettings.fixedFee;\n        const percentageFee = amount * withdrawalSettings.percentageFee / 100;\n        const totalFees = fixedFee + percentageFee;\n        const totalDeduction = amount + totalFees;\n        const netAmount = amount;\n        return {\n            fixedFee,\n            percentageFee,\n            totalFees,\n            totalDeduction,\n            netAmount\n        };\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-32 bg-gray-200 rounded-xl\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                    lineNumber: 427,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                lineNumber: 426,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n            lineNumber: 425,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!walletData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                className: \"text-center py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-500\",\n                    children: \"Failed to load wallet data\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                    lineNumber: 437,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                lineNumber: 436,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n            lineNumber: 435,\n            columnNumber: 7\n        }, undefined);\n    }\n    const tabs = [\n        {\n            id: 'overview',\n            label: 'Overview',\n            icon: _barrel_optimize_names_AlertTriangle_ArrowDownLeft_ArrowUpRight_CheckCircle_Clock_Copy_Filter_RefreshCw_Search_Shield_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        },\n        {\n            id: 'deposit',\n            label: 'Deposit',\n            icon: _barrel_optimize_names_AlertTriangle_ArrowDownLeft_ArrowUpRight_CheckCircle_Clock_Copy_Filter_RefreshCw_Search_Shield_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        },\n        {\n            id: 'withdraw',\n            label: 'Withdraw',\n            icon: _barrel_optimize_names_AlertTriangle_ArrowDownLeft_ArrowUpRight_CheckCircle_Clock_Copy_Filter_RefreshCw_Search_Shield_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        }\n    ];\n    const renderTabContent = ()=>{\n        switch(activeTab){\n            case 'deposit':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_wallet_DepositPage__WEBPACK_IMPORTED_MODULE_5__.DepositPage, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                    lineNumber: 452,\n                    columnNumber: 16\n                }, undefined);\n            case 'withdraw':\n                return renderWithdrawContent();\n            default:\n                return renderOverviewContent();\n        }\n    };\n    const renderOverviewContent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout__WEBPACK_IMPORTED_MODULE_3__.Grid, {\n                        cols: {\n                            default: 1,\n                            lg: 2\n                        },\n                        gap: 8,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600 mb-2\",\n                                                            children: \"Available Balance\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                            lineNumber: 469,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-4xl font-bold text-dark-900\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(walletData.balance)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                            lineNumber: 470,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                    lineNumber: 468,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-16 w-16 bg-eco-100 rounded-xl flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowDownLeft_ArrowUpRight_CheckCircle_Clock_Copy_Filter_RefreshCw_Search_Shield_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-8 w-8 text-eco-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                        lineNumber: 475,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                    lineNumber: 474,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                            lineNumber: 467,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 sm:grid-cols-2 gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    onClick: ()=>setActiveTab('deposit'),\n                                                    variant: \"outline\",\n                                                    className: \"h-12 text-base font-semibold rounded-xl\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowDownLeft_ArrowUpRight_CheckCircle_Clock_Copy_Filter_RefreshCw_Search_Shield_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-5 w-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                            lineNumber: 484,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \"Deposit\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    onClick: ()=>setActiveTab('withdraw'),\n                                                    className: \"h-12 text-base font-semibold rounded-xl\",\n                                                    disabled: walletData.balance < 10,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowDownLeft_ArrowUpRight_CheckCircle_Clock_Copy_Filter_RefreshCw_Search_Shield_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"h-5 w-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                            lineNumber: 492,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \"Withdraw\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                    lineNumber: 487,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                    lineNumber: 466,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                lineNumber: 465,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600 mb-2\",\n                                                            children: \"Pending Earnings\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                            lineNumber: 503,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-4xl font-bold text-solar-600\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(walletData.pendingEarnings)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                            lineNumber: 504,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                    lineNumber: 502,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-16 w-16 bg-solar-100 rounded-xl flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowDownLeft_ArrowUpRight_CheckCircle_Clock_Copy_Filter_RefreshCw_Search_Shield_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-8 w-8 text-solar-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                        lineNumber: 509,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                            lineNumber: 501,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 font-medium\",\n                                            children: \"Will be transferred on Saturday at 15:00 UTC\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                            lineNumber: 512,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                    lineNumber: 500,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                lineNumber: 499,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                        lineNumber: 464,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                    lineNumber: 463,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row sm:items-center justify-between gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        children: \"Transaction History\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                        lineNumber: 524,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row items-stretch sm:items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>setShowFilters(!showFilters),\n                                                className: \"flex items-center justify-center space-x-2 w-full sm:w-auto\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowDownLeft_ArrowUpRight_CheckCircle_Clock_Copy_Filter_RefreshCw_Search_Shield_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                        lineNumber: 532,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Filters\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                        lineNumber: 533,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                lineNumber: 526,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: fetchAllTransactions,\n                                                disabled: transactionLoading,\n                                                className: \"flex items-center justify-center space-x-2 w-full sm:w-auto\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowDownLeft_ArrowUpRight_CheckCircle_Clock_Copy_Filter_RefreshCw_Search_Shield_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-4 w-4 \".concat(transactionLoading ? 'animate-spin' : '')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Refresh\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                        lineNumber: 543,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                lineNumber: 535,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                lineNumber: 523,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                            lineNumber: 522,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowDownLeft_ArrowUpRight_CheckCircle_Clock_Copy_Filter_RefreshCw_Search_Shield_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                                    placeholder: \"Search transactions by description, type, or reference...\",\n                                                    value: searchTerm,\n                                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                                    className: \"pl-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                    lineNumber: 554,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                            lineNumber: 552,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 sm:grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Transaction Type\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                            lineNumber: 566,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: filterType,\n                                                            onChange: (e)=>setFilterType(e.target.value),\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-eco-500 text-sm\",\n                                                            children: transactionTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: type,\n                                                                    children: type === 'ALL' ? 'All Types' : type.replace('_', ' ')\n                                                                }, type, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                                    lineNumber: 575,\n                                                                    columnNumber: 23\n                                                                }, undefined))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                            lineNumber: 569,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                    lineNumber: 565,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Status\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                            lineNumber: 582,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: filterStatus,\n                                                            onChange: (e)=>setFilterStatus(e.target.value),\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-eco-500 text-sm\",\n                                                            children: statusOptions.map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: status,\n                                                                    children: status === 'ALL' ? 'All Status' : status.replace('_', ' ')\n                                                                }, status, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                                    lineNumber: 591,\n                                                                    columnNumber: 23\n                                                                }, undefined))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                            lineNumber: 585,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                    lineNumber: 581,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                            lineNumber: 564,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                    lineNumber: 550,\n                                    columnNumber: 11\n                                }, undefined),\n                                transactionLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowDownLeft_ArrowUpRight_CheckCircle_Clock_Copy_Filter_RefreshCw_Search_Shield_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-8 w-8 animate-spin mx-auto text-gray-400 mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                            lineNumber: 604,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500\",\n                                            children: \"Loading transactions...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                            lineNumber: 605,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                    lineNumber: 603,\n                                    columnNumber: 13\n                                }, undefined) : allTransactions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: allTransactions.map((transaction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row sm:items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer gap-3\",\n                                            onClick: ()=>handleTransactionClick(transaction),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start sm:items-center space-x-3 flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-shrink-0 mt-1 sm:mt-0\",\n                                                            children: getTransactionIcon(transaction.type)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                            lineNumber: 616,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 min-w-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium text-dark-900 truncate\",\n                                                                    children: transaction.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                                    lineNumber: 620,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-col sm:flex-row sm:items-center sm:space-x-4 text-sm text-gray-500 mt-1 gap-1 sm:gap-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs sm:text-sm\",\n                                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatDateTime)(transaction.createdAt)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                                            lineNumber: 622,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-2 py-1 bg-gray-200 rounded text-xs w-fit\",\n                                                                            children: transaction.type.replace('_', ' ')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                                            lineNumber: 623,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        transaction.reference && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs cursor-pointer hover:bg-blue-200 w-fit\",\n                                                                            onClick: (e)=>{\n                                                                                e.stopPropagation();\n                                                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.copyToClipboard)(transaction.reference);\n                                                                            },\n                                                                            title: \"Click to copy reference\",\n                                                                            children: [\n                                                                                \"Ref: \",\n                                                                                transaction.reference.substring(0, 8),\n                                                                                \"...\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                                            lineNumber: 627,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                                    lineNumber: 621,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                            lineNumber: 619,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                    lineNumber: 615,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between sm:justify-end sm:space-x-3 gap-2 flex-shrink-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold text-lg sm:text-base \".concat(getTransactionColor(transaction.type)),\n                                                            children: [\n                                                                transaction.type === 'WITHDRAWAL' || transaction.type === 'PURCHASE' ? '-' : '+',\n                                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(transaction.amount)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                            lineNumber: 642,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-shrink-0\",\n                                                            children: getStatusIcon(transaction.status)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                            lineNumber: 646,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                    lineNumber: 641,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, transaction.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                            lineNumber: 610,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                    lineNumber: 608,\n                                    columnNumber: 13\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500\",\n                                            children: \"No transactions found\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                            lineNumber: 655,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        (searchTerm || filterType !== 'ALL' || filterStatus !== 'ALL') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-400 mt-2\",\n                                            children: \"Try adjusting your search or filter criteria\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                            lineNumber: 657,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                    lineNumber: 654,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                            lineNumber: 548,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                    lineNumber: 521,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n            lineNumber: 461,\n            columnNumber: 5\n        }, undefined);\n    const renderWithdrawContent = ()=>{\n        const amount = parseFloat(withdrawalForm.amount) || 0;\n        const feeCalculation = calculateWithdrawalFees(amount);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: \"Withdraw USDT\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                            lineNumber: 676,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-1\",\n                            children: \"Withdraw funds from your wallet to your USDT TRC20 address\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                            lineNumber: 677,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                    lineNumber: 675,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-white border-gray-200 shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 text-sm\",\n                                                    children: \"Available Balance\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                    lineNumber: 686,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: [\n                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)((walletData === null || walletData === void 0 ? void 0 : walletData.balance) || 0),\n                                                        \" USDT\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                    lineNumber: 687,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                            lineNumber: 685,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowDownLeft_ArrowUpRight_CheckCircle_Clock_Copy_Filter_RefreshCw_Search_Shield_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-8 h-8 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                            lineNumber: 689,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                    lineNumber: 684,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                lineNumber: 683,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                            lineNumber: 682,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-white border-gray-200 shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 text-sm\",\n                                                    children: \"Minimum Withdrawal\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                    lineNumber: 698,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: [\n                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)((withdrawalSettings === null || withdrawalSettings === void 0 ? void 0 : withdrawalSettings.minWithdrawalAmount) || 10),\n                                                        \" USDT\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                    lineNumber: 699,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                            lineNumber: 697,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowDownLeft_ArrowUpRight_CheckCircle_Clock_Copy_Filter_RefreshCw_Search_Shield_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-8 h-8 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                            lineNumber: 701,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                    lineNumber: 696,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                lineNumber: 695,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                            lineNumber: 694,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-white border-gray-200 shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 text-sm\",\n                                                    children: \"Network\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                    lineNumber: 710,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: \"USDT (TRC20)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                    lineNumber: 711,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                            lineNumber: 709,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowDownLeft_ArrowUpRight_CheckCircle_Clock_Copy_Filter_RefreshCw_Search_Shield_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-8 h-8 text-purple-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                            lineNumber: 713,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                    lineNumber: 708,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                lineNumber: 707,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                            lineNumber: 706,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                    lineNumber: 681,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    className: \"bg-white border-gray-200 shadow-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-gray-900\",\n                                children: \"Withdrawal Details\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                lineNumber: 722,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                            lineNumber: 721,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleWithdrawalSubmit,\n                                className: \"space-y-6\",\n                                children: [\n                                    withdrawalError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg text-sm\",\n                                        children: withdrawalError\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                        lineNumber: 727,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"Withdrawal Amount (USDT)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                                lineNumber: 736,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                                                type: \"number\",\n                                                                step: \"0.01\",\n                                                                min: (withdrawalSettings === null || withdrawalSettings === void 0 ? void 0 : withdrawalSettings.minWithdrawalAmount) || 10,\n                                                                max: (walletData === null || walletData === void 0 ? void 0 : walletData.balance) || 0,\n                                                                value: withdrawalForm.amount,\n                                                                onChange: (e)=>setWithdrawalForm((prev)=>({\n                                                                            ...prev,\n                                                                            amount: e.target.value\n                                                                        })),\n                                                                placeholder: \"Enter amount to withdraw\",\n                                                                className: \"bg-white border-gray-300 text-gray-900\",\n                                                                required: true\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                                lineNumber: 739,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mt-1\",\n                                                                children: [\n                                                                    \"Min: \",\n                                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)((withdrawalSettings === null || withdrawalSettings === void 0 ? void 0 : withdrawalSettings.minWithdrawalAmount) || 10),\n                                                                    \" USDT\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                                lineNumber: 750,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                        lineNumber: 735,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"USDT TRC20 Address\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                                lineNumber: 756,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                                                type: \"text\",\n                                                                value: withdrawalForm.usdtAddress,\n                                                                onChange: (e)=>setWithdrawalForm((prev)=>({\n                                                                            ...prev,\n                                                                            usdtAddress: e.target.value\n                                                                        })),\n                                                                placeholder: \"Enter your USDT TRC20 address\",\n                                                                className: \"bg-white border-gray-300 text-gray-900\",\n                                                                required: true\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                                lineNumber: 759,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mt-1\",\n                                                                children: userWithdrawalAddress && withdrawalForm.usdtAddress === userWithdrawalAddress ? 'Auto-filled from your saved withdrawal address' : 'Only TRC20 addresses are supported'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                                lineNumber: 767,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                        lineNumber: 755,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                lineNumber: 734,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            amount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                className: \"bg-gray-50 border-gray-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                            className: \"text-lg text-gray-900\",\n                                                            children: \"Transaction Summary\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                            lineNumber: 780,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                        lineNumber: 779,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-600\",\n                                                                            children: \"Withdrawal Amount:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                                            lineNumber: 785,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-semibold text-gray-900\",\n                                                                            children: [\n                                                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(amount),\n                                                                                \" USDT\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                                            lineNumber: 786,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                                    lineNumber: 784,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-600\",\n                                                                            children: \"Fixed Fee:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                                            lineNumber: 789,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-semibold text-red-600\",\n                                                                            children: [\n                                                                                \"-\",\n                                                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(feeCalculation.fixedFee),\n                                                                                \" USDT\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                                            lineNumber: 790,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                                    lineNumber: 788,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-600\",\n                                                                            children: [\n                                                                                \"Percentage Fee (\",\n                                                                                (withdrawalSettings === null || withdrawalSettings === void 0 ? void 0 : withdrawalSettings.percentageFee) || 0,\n                                                                                \"%):\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                                            lineNumber: 793,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-semibold text-red-600\",\n                                                                            children: [\n                                                                                \"-\",\n                                                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(feeCalculation.percentageFee),\n                                                                                \" USDT\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                                            lineNumber: 794,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                                    lineNumber: 792,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"border-t border-gray-300 pt-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex justify-between items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-gray-900 font-medium\",\n                                                                                    children: \"Total Fees:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                                                    lineNumber: 798,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-bold text-red-600\",\n                                                                                    children: [\n                                                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(feeCalculation.totalFees),\n                                                                                        \" USDT\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                                                    lineNumber: 799,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                                            lineNumber: 797,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex justify-between items-center mt-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-gray-900 font-medium\",\n                                                                                    children: \"Total Deduction:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                                                    lineNumber: 802,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-bold text-red-600\",\n                                                                                    children: [\n                                                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(feeCalculation.totalDeduction),\n                                                                                        \" USDT\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                                                    lineNumber: 803,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                                            lineNumber: 801,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex justify-between items-center mt-3 p-3 bg-green-50 rounded-lg\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-green-800 font-bold text-lg\",\n                                                                                    children: \"You Receive:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                                                    lineNumber: 806,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-bold text-green-600 text-xl\",\n                                                                                    children: [\n                                                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(feeCalculation.netAmount),\n                                                                                        \" USDT\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                                                    lineNumber: 807,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                                            lineNumber: 805,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                                    lineNumber: 796,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                            lineNumber: 783,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                        lineNumber: 782,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                lineNumber: 778,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                        lineNumber: 732,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowDownLeft_ArrowUpRight_CheckCircle_Clock_Copy_Filter_RefreshCw_Search_Shield_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                    lineNumber: 819,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-yellow-800 mb-2\",\n                                                            children: \"Important Information\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                            lineNumber: 821,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"text-sm text-yellow-700 space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"• Only USDT TRC20 addresses are supported\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                                    lineNumber: 823,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"• Withdrawals require KYC verification\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                                    lineNumber: 824,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: [\n                                                                        \"• Processing time: \",\n                                                                        (withdrawalSettings === null || withdrawalSettings === void 0 ? void 0 : withdrawalSettings.processingDays) || 3,\n                                                                        \" business days\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                                    lineNumber: 825,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"• Double-check your address - transactions cannot be reversed\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                                    lineNumber: 826,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"• Fees are deducted from your balance in addition to the withdrawal amount\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                                    lineNumber: 827,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                            lineNumber: 822,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                    lineNumber: 820,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                            lineNumber: 818,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                        lineNumber: 817,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                type: \"button\",\n                                                variant: \"outline\",\n                                                onClick: ()=>setActiveTab('overview'),\n                                                className: \"flex-1 border-gray-300 text-gray-700 hover:bg-gray-50\",\n                                                children: \"Back to Overview\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                lineNumber: 835,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                type: \"submit\",\n                                                loading: withdrawalLoading,\n                                                className: \"flex-1 bg-yellow-500 hover:bg-yellow-600 text-white\",\n                                                disabled: !walletData || !withdrawalSettings || amount < ((withdrawalSettings === null || withdrawalSettings === void 0 ? void 0 : withdrawalSettings.minWithdrawalAmount) || 10) || feeCalculation.totalDeduction > walletData.balance,\n                                                children: feeCalculation.totalDeduction > ((walletData === null || walletData === void 0 ? void 0 : walletData.balance) || 0) ? 'Insufficient Balance' : 'Submit Withdrawal'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                lineNumber: 843,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                        lineNumber: 834,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                lineNumber: 725,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                            lineNumber: 724,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                    lineNumber: 720,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n            lineNumber: 673,\n            columnNumber: 7\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"-mb-px flex space-x-8\",\n                    children: tabs.map((tab)=>{\n                        const Icon = tab.icon;\n                        const isActive = activeTab === tab.id;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveTab(tab.id),\n                            className: \"\\n                  flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors\\n                  \".concat(isActive ? 'border-solar-500 text-solar-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300', \"\\n                \"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                    lineNumber: 887,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: tab.label\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                    lineNumber: 888,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, tab.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                            lineNumber: 876,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                    lineNumber: 871,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                lineNumber: 870,\n                columnNumber: 7\n            }, undefined),\n            renderTabContent(),\n            showTransactionModal && selectedTransaction && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Modal, {\n                isOpen: showTransactionModal,\n                onClose: ()=>setShowTransactionModal(false),\n                title: \"Transaction Details\",\n                size: \"lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 bg-gray-50 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        getTransactionIcon(selectedTransaction.type),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-lg text-dark-900\",\n                                                    children: selectedTransaction.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                    lineNumber: 912,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: [\n                                                        selectedTransaction.type.replace('_', ' '),\n                                                        \" • \",\n                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatDateTime)(selectedTransaction.createdAt)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                    lineNumber: 915,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                            lineNumber: 911,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                    lineNumber: 909,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 mb-1\",\n                                            children: [\n                                                getStatusIcon(selectedTransaction.status),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-3 py-1 rounded-full text-sm font-medium \".concat(selectedTransaction.status === 'COMPLETED' || selectedTransaction.status === 'CONFIRMED' ? 'bg-eco-100 text-eco-700' : selectedTransaction.status === 'PENDING' ? 'bg-solar-100 text-solar-700' : 'bg-red-100 text-red-700'),\n                                                    children: selectedTransaction.status\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                    lineNumber: 923,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                            lineNumber: 921,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl font-bold \".concat(getTransactionColor(selectedTransaction.type)),\n                                            children: [\n                                                selectedTransaction.type === 'WITHDRAWAL' || selectedTransaction.type === 'PURCHASE' ? '-' : '+',\n                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(selectedTransaction.amount)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                            lineNumber: 931,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                    lineNumber: 920,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                            lineNumber: 908,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Transaction ID\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                    lineNumber: 942,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-900 font-mono bg-gray-100 px-2 py-1 rounded break-all flex-1 min-w-0\",\n                                                            children: selectedTransaction.id\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                            lineNumber: 944,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>(0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.copyToClipboard)(selectedTransaction.id),\n                                                            className: \"text-gray-400 hover:text-gray-600 flex-shrink-0 mt-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowDownLeft_ArrowUpRight_CheckCircle_Clock_Copy_Filter_RefreshCw_Search_Shield_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                                lineNumber: 951,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                            lineNumber: 947,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                    lineNumber: 943,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                            lineNumber: 941,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Type\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                    lineNumber: 957,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-900\",\n                                                    children: selectedTransaction.type.replace('_', ' ')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                    lineNumber: 958,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                            lineNumber: 956,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                    lineNumber: 962,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        getStatusIcon(selectedTransaction.status),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-900\",\n                                                            children: selectedTransaction.status\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                            lineNumber: 965,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                    lineNumber: 963,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                            lineNumber: 961,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Date & Time\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                    lineNumber: 970,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-900\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatDateTime)(selectedTransaction.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                    lineNumber: 971,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                            lineNumber: 969,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                    lineNumber: 940,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        selectedTransaction.reference && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Reference\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                    lineNumber: 978,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-900 font-mono bg-gray-100 px-2 py-1 rounded break-all flex-1 min-w-0\",\n                                                            children: selectedTransaction.reference\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                            lineNumber: 980,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>(0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.copyToClipboard)(selectedTransaction.reference),\n                                                            className: \"text-gray-400 hover:text-gray-600 flex-shrink-0 mt-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowDownLeft_ArrowUpRight_CheckCircle_Clock_Copy_Filter_RefreshCw_Search_Shield_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                                lineNumber: 987,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                            lineNumber: 983,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                    lineNumber: 979,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                            lineNumber: 977,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        selectedTransaction.txid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Transaction Hash\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                    lineNumber: 995,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-900 font-mono bg-gray-100 px-2 py-1 rounded break-all flex-1 min-w-0\",\n                                                            children: selectedTransaction.txid\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                            lineNumber: 997,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>(0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.copyToClipboard)(selectedTransaction.txid),\n                                                            className: \"text-gray-400 hover:text-gray-600 flex-shrink-0 mt-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowDownLeft_ArrowUpRight_CheckCircle_Clock_Copy_Filter_RefreshCw_Search_Shield_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                                lineNumber: 1004,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                            lineNumber: 1000,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                    lineNumber: 996,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                            lineNumber: 994,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        selectedTransaction.usdtAddress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: selectedTransaction.type === 'DEPOSIT' ? 'Deposit Address' : 'Withdrawal Address'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                    lineNumber: 1012,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-900 font-mono bg-gray-100 px-2 py-1 rounded break-all\",\n                                                            children: selectedTransaction.usdtAddress\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                            lineNumber: 1016,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>(0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.copyToClipboard)(selectedTransaction.usdtAddress),\n                                                            className: \"text-gray-400 hover:text-gray-600 flex-shrink-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowDownLeft_ArrowUpRight_CheckCircle_Clock_Copy_Filter_RefreshCw_Search_Shield_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                                lineNumber: 1023,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                            lineNumber: 1019,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                    lineNumber: 1015,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                            lineNumber: 1011,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        selectedTransaction.confirmations !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Confirmations\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                    lineNumber: 1031,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-900\",\n                                                    children: selectedTransaction.confirmations\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                    lineNumber: 1032,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                            lineNumber: 1030,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        selectedTransaction.processedAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Processed At\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                    lineNumber: 1038,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-900\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatDateTime)(selectedTransaction.processedAt)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                    lineNumber: 1039,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                            lineNumber: 1037,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                    lineNumber: 975,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                            lineNumber: 939,\n                            columnNumber: 13\n                        }, undefined),\n                        selectedTransaction.rejectionReason && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium text-red-800 mb-2\",\n                                    children: \"Rejection Reason\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                    lineNumber: 1048,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-red-700\",\n                                    children: selectedTransaction.rejectionReason\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                    lineNumber: 1049,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                            lineNumber: 1047,\n                            columnNumber: 15\n                        }, undefined),\n                        selectedTransaction.senderAddress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium text-blue-800 mb-2\",\n                                    children: \"Sender Information\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                    lineNumber: 1055,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-blue-700 font-mono\",\n                                            children: selectedTransaction.senderAddress\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                            lineNumber: 1057,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>(0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.copyToClipboard)(selectedTransaction.senderAddress),\n                                            className: \"text-blue-400 hover:text-blue-600\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowDownLeft_ArrowUpRight_CheckCircle_Clock_Copy_Filter_RefreshCw_Search_Shield_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                                lineNumber: 1062,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                            lineNumber: 1058,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                    lineNumber: 1056,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                            lineNumber: 1054,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-3 pt-4 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setShowTransactionModal(false),\n                                    children: \"Close\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                    lineNumber: 1070,\n                                    columnNumber: 15\n                                }, undefined),\n                                selectedTransaction.txid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: ()=>{\n                                        const explorerUrl = \"https://tronscan.org/#/transaction/\".concat(selectedTransaction.txid);\n                                        window.open(explorerUrl, '_blank');\n                                    },\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"View on Explorer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                            lineNumber: 1084,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowDownLeft_ArrowUpRight_CheckCircle_Clock_Copy_Filter_RefreshCw_Search_Shield_Wallet_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                            lineNumber: 1085,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                                    lineNumber: 1077,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                            lineNumber: 1069,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                    lineNumber: 906,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                lineNumber: 900,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ConfirmDialog, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                lineNumber: 1094,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageBoxComponent, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n                lineNumber: 1095,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\dashboard\\\\WalletDashboard.tsx\",\n        lineNumber: 868,\n        columnNumber: 5\n    }, undefined);\n};\n_s(WalletDashboard, \"LZKuc+7PtJWbGVsz9NFXhhFlP0E=\", false, function() {\n    return [\n        _components_ui__WEBPACK_IMPORTED_MODULE_2__.useConfirmDialog,\n        _components_ui__WEBPACK_IMPORTED_MODULE_2__.useMessageBox\n    ];\n});\n_c = WalletDashboard;\nvar _c;\n$RefreshReg$(_c, \"WalletDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/WalletDashboard.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/Button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/Button.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(app-pages-browser)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Button,buttonVariants auto */ \n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)('inline-flex items-center justify-center rounded-xl font-semibold focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none shadow-lg', {\n    variants: {\n        variant: {\n            primary: 'bg-yellow-500 text-white focus:ring-yellow-500',\n            secondary: 'bg-gray-200 text-gray-800 focus:ring-gray-500',\n            success: 'bg-emerald-500 text-white focus:ring-emerald-500',\n            danger: 'bg-red-500 text-white focus:ring-red-500',\n            warning: 'bg-yellow-500 text-white focus:ring-yellow-500',\n            destructive: 'bg-red-600 text-white focus:ring-red-500',\n            outline: 'border-2 border-yellow-500 bg-transparent text-yellow-600 focus:ring-yellow-500',\n            ghost: 'text-gray-600 focus:ring-yellow-500 rounded-lg',\n            link: 'text-yellow-600 underline-offset-4 focus:ring-yellow-500',\n            premium: 'bg-slate-800 text-white focus:ring-slate-500',\n            glass: 'glass-morphism text-slate-900 backdrop-blur-xl border border-white/20'\n        },\n        size: {\n            sm: 'h-10 px-4 text-sm rounded-lg',\n            md: 'h-12 px-6 text-base rounded-xl',\n            lg: 'h-14 px-8 text-lg rounded-xl',\n            xl: 'h-16 px-10 text-xl rounded-2xl font-bold',\n            icon: 'h-12 w-12 rounded-xl'\n        }\n    },\n    defaultVariants: {\n        variant: 'primary',\n        size: 'md'\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(_c = (param, ref)=>{\n    let { className, variant, size, loading, leftIcon, rightIcon, children, disabled, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        disabled: disabled || loading,\n        ...props,\n        children: [\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mr-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"spinner\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                lineNumber: 57,\n                columnNumber: 11\n            }, undefined),\n            leftIcon && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"mr-2\",\n                children: leftIcon\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                lineNumber: 61,\n                columnNumber: 34\n            }, undefined),\n            children,\n            rightIcon && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"ml-2\",\n                children: rightIcon\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                lineNumber: 63,\n                columnNumber: 35\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n        lineNumber: 50,\n        columnNumber: 7\n    }, undefined);\n});\n_c1 = Button;\nButton.displayName = 'Button';\n\nvar _c, _c1;\n$RefreshReg$(_c, \"Button$React.forwardRef\");\n$RefreshReg$(_c1, \"Button\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/Button.tsx\n"));

/***/ })

});