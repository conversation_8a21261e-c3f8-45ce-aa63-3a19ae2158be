/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/register/route";
exports.ids = ["app/api/auth/register/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fregister%2Froute&page=%2Fapi%2Fauth%2Fregister%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fregister%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fregister%2Froute&page=%2Fapi%2Fauth%2Fregister%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fregister%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_dream_Desktop_Hash_Minings_hashcorex_src_app_api_auth_register_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/register/route.ts */ \"(rsc)/./src/app/api/auth/register/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/register/route\",\n        pathname: \"/api/auth/register\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/register/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\api\\\\auth\\\\register\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_dream_Desktop_Hash_Minings_hashcorex_src_app_api_auth_register_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fregister%2Froute&page=%2Fapi%2Fauth%2Fregister%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fregister%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/register/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/auth/register/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./src/lib/database.ts\");\n/* harmony import */ var _lib_rateLimiter__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/rateLimiter */ \"(rsc)/./src/lib/rateLimiter.ts\");\n/* harmony import */ var _lib_secureErrorHandler__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/secureErrorHandler */ \"(rsc)/./src/lib/secureErrorHandler.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\n\n\n// import { validateInput, registerUserSchema } from '@/lib/validation';\n\n\nconst registerHandler = async (request)=>{\n    // Check rate limiting first\n    const rateLimitResult = (0,_lib_rateLimiter__WEBPACK_IMPORTED_MODULE_3__.registerRateLimit)(request);\n    if (!rateLimitResult.allowed) {\n        const error = (0,_lib_secureErrorHandler__WEBPACK_IMPORTED_MODULE_4__.createRateLimitError)(rateLimitResult.retryAfter, rateLimitResult.remaining, rateLimitResult.resetTime);\n        return (0,_lib_secureErrorHandler__WEBPACK_IMPORTED_MODULE_4__.createSecureResponse)(error);\n    }\n    // Check if registration is enabled\n    const registrationEnabled = await _lib_database__WEBPACK_IMPORTED_MODULE_2__.adminSettingsDb.get('registrationEnabled');\n    if (registrationEnabled === 'false') {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Registration is currently disabled'\n        }, {\n            status: 403\n        });\n    }\n    const body = await request.json();\n    const { otp, placementSide, agreeToTerms = true, ...userData } = body;\n    // Basic input validation\n    const { email, firstName, lastName, password, confirmPassword, referralCode } = userData;\n    if (!email || !firstName || !lastName || !password || !confirmPassword) {\n        const error = (0,_lib_secureErrorHandler__WEBPACK_IMPORTED_MODULE_4__.createValidationError)('All fields are required');\n        return (0,_lib_secureErrorHandler__WEBPACK_IMPORTED_MODULE_4__.createSecureResponse)(error);\n    }\n    if (!(0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.validateEmail)(email)) {\n        const error = (0,_lib_secureErrorHandler__WEBPACK_IMPORTED_MODULE_4__.createValidationError)('Invalid email format');\n        return (0,_lib_secureErrorHandler__WEBPACK_IMPORTED_MODULE_4__.createSecureResponse)(error);\n    }\n    if (password !== confirmPassword) {\n        const error = (0,_lib_secureErrorHandler__WEBPACK_IMPORTED_MODULE_4__.createValidationError)('Passwords do not match');\n        return (0,_lib_secureErrorHandler__WEBPACK_IMPORTED_MODULE_4__.createSecureResponse)(error);\n    }\n    const passwordValidation = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.validatePassword)(password);\n    if (!passwordValidation.isValid) {\n        const error = (0,_lib_secureErrorHandler__WEBPACK_IMPORTED_MODULE_4__.createValidationError)(passwordValidation.errors.join(', '));\n        return (0,_lib_secureErrorHandler__WEBPACK_IMPORTED_MODULE_4__.createSecureResponse)(error);\n    }\n    const validatedData = {\n        email: email.toLowerCase().trim(),\n        firstName: firstName.trim(),\n        lastName: lastName.trim(),\n        password,\n        referralCode: referralCode?.trim() || undefined\n    };\n    // Verify OTP before registration\n    if (!otp) {\n        const error = (0,_lib_secureErrorHandler__WEBPACK_IMPORTED_MODULE_4__.createValidationError)('OTP is required for registration');\n        return (0,_lib_secureErrorHandler__WEBPACK_IMPORTED_MODULE_4__.createSecureResponse)(error);\n    }\n    const otpRecord = await _lib_database__WEBPACK_IMPORTED_MODULE_2__.otpDb.findValid(validatedData.email, 'email_verification');\n    if (!otpRecord || otpRecord.otp !== otp) {\n        const error = (0,_lib_secureErrorHandler__WEBPACK_IMPORTED_MODULE_4__.createValidationError)('Invalid or expired OTP. Please request a new one.');\n        return (0,_lib_secureErrorHandler__WEBPACK_IMPORTED_MODULE_4__.createSecureResponse)(error);\n    }\n    // Mark OTP as verified\n    await _lib_database__WEBPACK_IMPORTED_MODULE_2__.otpDb.verify(otpRecord.id);\n    // Extract side parameter from URL if present\n    const url = new URL(request.url);\n    const side = url.searchParams.get('side');\n    // Register user with validated data\n    const user = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.registerUser)({\n        email: validatedData.email,\n        firstName: validatedData.firstName,\n        lastName: validatedData.lastName,\n        password: validatedData.password,\n        referralCode: validatedData.referralCode || undefined,\n        placementSide: placementSide || side || undefined\n    });\n    // Log successful registration\n    await _lib_database__WEBPACK_IMPORTED_MODULE_2__.systemLogDb.create({\n        action: 'USER_REGISTRATION_SUCCESS',\n        userId: user.id,\n        details: {\n            email: user.email,\n            referralCode: validatedData.referralCode || null,\n            placementSide: placementSide || side || null,\n            registrationTime: new Date().toISOString()\n        },\n        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',\n        userAgent: request.headers.get('user-agent') || 'unknown'\n    });\n    // Generate token for automatic login after registration\n    const token = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.generateToken)({\n        userId: user.id,\n        email: user.email\n    });\n    // Set secure HTTP-only cookie for automatic login\n    const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        success: true,\n        message: 'Registration successful',\n        data: {\n            user: {\n                id: user.id,\n                email: user.email,\n                firstName: user.firstName,\n                lastName: user.lastName,\n                referralId: user.referralId,\n                kycStatus: user.kycStatus,\n                role: user.role\n            }\n        }\n    });\n    // Enhanced cookie security\n    response.cookies.set('auth-token', token, {\n        httpOnly: true,\n        secure: \"development\" === 'production',\n        sameSite: 'strict',\n        maxAge: 30 * 24 * 60 * 60,\n        path: '/'\n    });\n    // Add security headers\n    response.headers.set('X-Content-Type-Options', 'nosniff');\n    response.headers.set('X-Frame-Options', 'DENY');\n    response.headers.set('X-XSS-Protection', '1; mode=block');\n    // Send welcome email (don't fail registration if email fails)\n    try {\n        const { emailNotificationService } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/nodemailer\"), __webpack_require__.e(\"_rsc_src_lib_emailNotificationService_ts\")]).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/emailNotificationService */ \"(rsc)/./src/lib/emailNotificationService.ts\"));\n        await emailNotificationService.sendWelcomeEmailNotification({\n            userId: user.id,\n            email: user.email,\n            firstName: user.firstName,\n            lastName: user.lastName\n        });\n    } catch (emailError) {\n        console.error('Failed to send welcome email:', emailError);\n        // Log email failure but don't fail the registration\n        await _lib_database__WEBPACK_IMPORTED_MODULE_2__.systemLogDb.create({\n            action: 'EMAIL_SEND_FAILED',\n            userId: user.id,\n            details: {\n                type: 'welcome_email',\n                error: emailError instanceof Error ? emailError.message : 'Unknown error'\n            },\n            ipAddress: request.headers.get('x-forwarded-for') || 'unknown',\n            userAgent: request.headers.get('user-agent') || 'unknown'\n        });\n    }\n    return response;\n};\n// Export the handler with secure error handling\nconst POST = (0,_lib_secureErrorHandler__WEBPACK_IMPORTED_MODULE_4__.withSecureErrorHandling)(registerHandler, {\n    endpoint: '/api/auth/register',\n    requireAuth: false\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/register/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticateRequest: () => (/* binding */ authenticateRequest),\n/* harmony export */   createSession: () => (/* binding */ createSession),\n/* harmony export */   generateReferralId: () => (/* binding */ generateReferralId),\n/* harmony export */   generateToken: () => (/* binding */ generateToken),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   isAdmin: () => (/* binding */ isAdmin),\n/* harmony export */   loginUser: () => (/* binding */ loginUser),\n/* harmony export */   registerUser: () => (/* binding */ registerUser),\n/* harmony export */   validateEmail: () => (/* binding */ validateEmail),\n/* harmony export */   validatePassword: () => (/* binding */ validatePassword),\n/* harmony export */   validateSession: () => (/* binding */ validateSession),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./database */ \"(rsc)/./src/lib/database.ts\");\n/* harmony import */ var _envValidation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./envValidation */ \"(rsc)/./src/lib/envValidation.ts\");\n\n\n\n\n// Password utilities\nconst hashPassword = async (password)=>{\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].hash(password, _envValidation__WEBPACK_IMPORTED_MODULE_3__.config.security.bcryptRounds());\n};\nconst verifyPassword = async (password, hashedPassword)=>{\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].compare(password, hashedPassword);\n};\n// JWT utilities\nconst generateToken = (payload)=>{\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, _envValidation__WEBPACK_IMPORTED_MODULE_3__.config.jwt.secret(), {\n        expiresIn: _envValidation__WEBPACK_IMPORTED_MODULE_3__.config.jwt.expiresIn()\n    });\n};\nconst verifyToken = (token)=>{\n    try {\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, _envValidation__WEBPACK_IMPORTED_MODULE_3__.config.jwt.secret());\n        return decoded;\n    } catch (error) {\n        return null;\n    }\n};\n// Generate unique referral ID\nconst generateReferralId = ()=>{\n    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\n    let result = 'HC'; // HashCoreX prefix\n    for(let i = 0; i < 8; i++){\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n};\n// Authentication middleware\nconst authenticateRequest = async (request)=>{\n    const token = request.headers.get('authorization')?.replace('Bearer ', '') || request.cookies.get('auth-token')?.value;\n    if (!token) {\n        return {\n            authenticated: false,\n            user: null\n        };\n    }\n    const decoded = verifyToken(token);\n    if (!decoded) {\n        return {\n            authenticated: false,\n            user: null\n        };\n    }\n    const user = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByEmail(decoded.email);\n    if (!user) {\n        return {\n            authenticated: false,\n            user: null\n        };\n    }\n    return {\n        authenticated: true,\n        user\n    };\n};\n// User registration\nconst registerUser = async (data)=>{\n    // Check if user already exists\n    const existingUser = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByEmail(data.email);\n    if (existingUser) {\n        throw new Error('User already exists with this email');\n    }\n    // Validate referral code if provided\n    let referrerId;\n    if (data.referralCode) {\n        const referrer = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByReferralId(data.referralCode);\n        if (!referrer) {\n            throw new Error('Invalid referral code');\n        }\n        referrerId = referrer.id;\n    }\n    // Hash password\n    const passwordHash = await hashPassword(data.password);\n    // Generate unique referral ID\n    let referralId;\n    let isUnique = false;\n    do {\n        referralId = generateReferralId();\n        const existing = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByReferralId(referralId);\n        isUnique = !existing;\n    }while (!isUnique);\n    // Create user in PostgreSQL\n    const user = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.create({\n        email: data.email,\n        firstName: data.firstName,\n        lastName: data.lastName,\n        password: passwordHash,\n        referralId\n    });\n    // Create referral relationship if referrer exists\n    if (referrerId) {\n        const { placeUserByReferralType } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_referral_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./referral */ \"(rsc)/./src/lib/referral.ts\"));\n        // Determine referral type based on placementSide parameter\n        let referralType = 'general';\n        if (data.placementSide === 'left') {\n            referralType = 'left';\n        } else if (data.placementSide === 'right') {\n            referralType = 'right';\n        }\n        // Place user using the new unified placement function\n        await placeUserByReferralType(referrerId, user.id, referralType);\n    }\n    return {\n        id: user.id,\n        email: user.email,\n        referralId: user.referralId,\n        kycStatus: user.kycStatus\n    };\n};\n// User login\nconst loginUser = async (data)=>{\n    const user = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByEmail(data.email);\n    if (!user) {\n        throw new Error('Invalid email or password');\n    }\n    const isValidPassword = await verifyPassword(data.password, user.password);\n    if (!isValidPassword) {\n        throw new Error('Invalid email or password');\n    }\n    const token = generateToken({\n        userId: user.id,\n        email: user.email\n    });\n    return {\n        token,\n        user: {\n            id: user.id,\n            email: user.email,\n            referralId: user.referralId,\n            kycStatus: user.kycStatus\n        }\n    };\n};\n// Password validation\nconst validatePassword = (password)=>{\n    const errors = [];\n    if (password.length < 8) {\n        errors.push('Password must be at least 8 characters long');\n    }\n    if (!/[A-Z]/.test(password)) {\n        errors.push('Password must contain at least one uppercase letter');\n    }\n    if (!/[a-z]/.test(password)) {\n        errors.push('Password must contain at least one lowercase letter');\n    }\n    if (!/\\d/.test(password)) {\n        errors.push('Password must contain at least one number');\n    }\n    if (!/[!@#$%^&*(),.?\":{}|<>]/.test(password)) {\n        errors.push('Password must contain at least one special character');\n    }\n    return {\n        valid: errors.length === 0,\n        errors\n    };\n};\n// Email validation\nconst validateEmail = (email)=>{\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n};\n// Session management\nconst createSession = (userId, email)=>{\n    return generateToken({\n        userId,\n        email\n    });\n};\nconst validateSession = (token)=>{\n    return verifyToken(token);\n};\n// Admin authentication\nconst isAdmin = async (userId)=>{\n    const user = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findById(userId);\n    return user?.role === 'ADMIN';\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adminSettingsDb: () => (/* binding */ adminSettingsDb),\n/* harmony export */   binaryPointsDb: () => (/* binding */ binaryPointsDb),\n/* harmony export */   depositTransactionDb: () => (/* binding */ depositTransactionDb),\n/* harmony export */   emailLogDb: () => (/* binding */ emailLogDb),\n/* harmony export */   emailTemplateDb: () => (/* binding */ emailTemplateDb),\n/* harmony export */   miningUnitDb: () => (/* binding */ miningUnitDb),\n/* harmony export */   otpDb: () => (/* binding */ otpDb),\n/* harmony export */   referralDb: () => (/* binding */ referralDb),\n/* harmony export */   supportTicketDb: () => (/* binding */ supportTicketDb),\n/* harmony export */   systemLogDb: () => (/* binding */ systemLogDb),\n/* harmony export */   systemSettingsDb: () => (/* binding */ systemSettingsDb),\n/* harmony export */   ticketResponseDb: () => (/* binding */ ticketResponseDb),\n/* harmony export */   transactionDb: () => (/* binding */ transactionDb),\n/* harmony export */   userDb: () => (/* binding */ userDb),\n/* harmony export */   walletBalanceDb: () => (/* binding */ walletBalanceDb),\n/* harmony export */   withdrawalDb: () => (/* binding */ withdrawalDb)\n/* harmony export */ });\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n// User Database Operations\nconst userDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.create({\n            data: {\n                email: data.email,\n                firstName: data.firstName,\n                lastName: data.lastName,\n                password: data.password,\n                referralId: data.referralId || ''\n            }\n        });\n    },\n    async findByEmail (email) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                email\n            },\n            include: {\n                miningUnits: true,\n                transactions: true,\n                binaryPoints: true\n            }\n        });\n    },\n    async findById (id) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                id\n            },\n            include: {\n                miningUnits: true,\n                transactions: true,\n                binaryPoints: true\n            }\n        });\n    },\n    async findByReferralId (referralId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                referralId\n            }\n        });\n    },\n    async update (id, data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id\n            },\n            data\n        });\n    },\n    async updateKYCStatus (userId, status) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id: userId\n            },\n            data: {\n                kycStatus: status\n            }\n        });\n    },\n    async updateWithdrawalAddress (email, withdrawalAddress) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                email\n            },\n            data: {\n                withdrawalAddress\n            }\n        });\n    },\n    async updateProfilePicture (id, profilePicture) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id\n            },\n            data: {\n                profilePicture\n            },\n            select: {\n                id: true,\n                email: true,\n                firstName: true,\n                lastName: true,\n                referralId: true,\n                role: true,\n                kycStatus: true,\n                profilePicture: true,\n                createdAt: true,\n                updatedAt: true\n            }\n        });\n    },\n    async updatePassword (id, hashedPassword) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id\n            },\n            data: {\n                password: hashedPassword\n            }\n        });\n    }\n};\n// Mining Unit Database Operations\nconst miningUnitDb = {\n    async create (data) {\n        const expiryDate = new Date();\n        expiryDate.setFullYear(expiryDate.getFullYear() + 2); // 24 months from now\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.create({\n            data: {\n                userId: data.userId,\n                thsAmount: data.thsAmount,\n                investmentAmount: data.investmentAmount,\n                dailyROI: data.dailyROI,\n                expiryDate\n            }\n        });\n    },\n    async findActiveByUserId (userId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findMany({\n            where: {\n                userId,\n                status: 'ACTIVE',\n                expiryDate: {\n                    gt: new Date()\n                }\n            }\n        });\n    },\n    async updateTotalEarned (unitId, amount) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.update({\n            where: {\n                id: unitId\n            },\n            data: {\n                totalEarned: {\n                    increment: amount\n                }\n            }\n        });\n    },\n    async expireUnit (unitId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.update({\n            where: {\n                id: unitId\n            },\n            data: {\n                status: 'EXPIRED'\n            }\n        });\n    },\n    async findAllActive () {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findMany({\n            where: {\n                status: 'ACTIVE',\n                expiryDate: {\n                    gt: new Date()\n                }\n            },\n            include: {\n                user: true\n            }\n        });\n    },\n    async updateEarnings (unitId, earningType, amount) {\n        const updateData = {\n            totalEarned: {\n                increment: amount\n            }\n        };\n        switch(earningType){\n            case 'mining':\n                updateData.miningEarnings = {\n                    increment: amount\n                };\n                break;\n            case 'referral':\n                updateData.referralEarnings = {\n                    increment: amount\n                };\n                break;\n            case 'binary':\n                updateData.binaryEarnings = {\n                    increment: amount\n                };\n                break;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.update({\n            where: {\n                id: unitId\n            },\n            data: updateData\n        });\n    }\n};\n// Transaction Database Operations\nconst transactionDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.create({\n            data: {\n                userId: data.userId,\n                type: data.type,\n                amount: data.amount,\n                description: data.description,\n                reference: data.reference,\n                status: data.status || 'PENDING'\n            }\n        });\n    },\n    async findByUserId (userId, filters) {\n        const where = {\n            userId\n        };\n        if (filters?.types && filters.types.length > 0) {\n            where.type = {\n                in: filters.types\n            };\n        }\n        if (filters?.status) {\n            where.status = filters.status;\n        }\n        if (filters?.search) {\n            where.OR = [\n                {\n                    description: {\n                        contains: filters.search,\n                        mode: 'insensitive'\n                    }\n                },\n                {\n                    type: {\n                        contains: filters.search,\n                        mode: 'insensitive'\n                    }\n                },\n                {\n                    reference: {\n                        contains: filters.search,\n                        mode: 'insensitive'\n                    }\n                }\n            ];\n        }\n        const include = filters?.includeUser ? {\n            user: {\n                select: {\n                    id: true,\n                    email: true,\n                    firstName: true,\n                    lastName: true\n                }\n            }\n        } : undefined;\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.findMany({\n            where,\n            include,\n            orderBy: {\n                createdAt: 'desc'\n            },\n            take: filters?.limit || 50,\n            skip: filters?.offset\n        });\n    },\n    async updateStatus (transactionId, status, additionalData) {\n        const updateData = {\n            status\n        };\n        if (additionalData?.amount !== undefined) {\n            updateData.amount = additionalData.amount;\n        }\n        if (additionalData?.description) {\n            updateData.description = additionalData.description;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.update({\n            where: {\n                id: transactionId\n            },\n            data: updateData\n        });\n    },\n    async findPendingByTypeAndDescription (userId, type, descriptionPattern) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.findFirst({\n            where: {\n                userId,\n                type: type,\n                description: {\n                    contains: descriptionPattern\n                },\n                status: 'PENDING'\n            }\n        });\n    },\n    async updateByReference (reference, type, status, additionalData) {\n        const updateData = {\n            status\n        };\n        if (additionalData?.amount !== undefined) {\n            updateData.amount = additionalData.amount;\n        }\n        if (additionalData?.description) {\n            updateData.description = additionalData.description;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.updateMany({\n            where: {\n                reference,\n                type: type,\n                status: 'PENDING'\n            },\n            data: updateData\n        });\n    }\n};\n// Referral Database Operations\nconst referralDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.create({\n            data: {\n                referrerId: data.referrerId,\n                referredId: data.referredId,\n                placementSide: data.placementSide\n            }\n        });\n    },\n    async findByReferrerId (referrerId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findMany({\n            where: {\n                referrerId\n            },\n            include: {\n                referred: {\n                    select: {\n                        id: true,\n                        email: true,\n                        createdAt: true\n                    }\n                }\n            }\n        });\n    }\n};\n// Binary Points Database Operations\nconst binaryPointsDb = {\n    async upsert (data) {\n        // Round to 2 decimal places to ensure precision\n        const leftPoints = data.leftPoints !== undefined ? Math.round(data.leftPoints * 100) / 100 : undefined;\n        const rightPoints = data.rightPoints !== undefined ? Math.round(data.rightPoints * 100) / 100 : undefined;\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.binaryPoints.upsert({\n            where: {\n                userId: data.userId\n            },\n            update: {\n                leftPoints: leftPoints !== undefined ? {\n                    increment: leftPoints\n                } : undefined,\n                rightPoints: rightPoints !== undefined ? {\n                    increment: rightPoints\n                } : undefined\n            },\n            create: {\n                userId: data.userId,\n                leftPoints: leftPoints || 0,\n                rightPoints: rightPoints || 0\n            }\n        });\n    },\n    async findByUserId (userId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.binaryPoints.findUnique({\n            where: {\n                userId\n            }\n        });\n    },\n    async resetPoints (userId, leftPoints, rightPoints) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.binaryPoints.update({\n            where: {\n                userId\n            },\n            data: {\n                leftPoints,\n                rightPoints,\n                flushDate: new Date()\n            }\n        });\n    }\n};\n// Withdrawal Database Operations\nconst withdrawalDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.withdrawalRequest.create({\n            data: {\n                userId: data.userId,\n                amount: data.amount,\n                usdtAddress: data.usdtAddress\n            }\n        });\n    },\n    async findPending () {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.withdrawalRequest.findMany({\n            where: {\n                status: 'PENDING'\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        kycStatus: true\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'asc'\n            }\n        });\n    },\n    async updateStatus (requestId, status, processedBy, txid, rejectionReason) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.withdrawalRequest.update({\n            where: {\n                id: requestId\n            },\n            data: {\n                status,\n                processedBy,\n                txid,\n                rejectionReason,\n                processedAt: new Date()\n            }\n        });\n    }\n};\n// Admin Settings Database Operations\nconst adminSettingsDb = {\n    async get (key) {\n        const setting = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.findUnique({\n            where: {\n                key\n            }\n        });\n        return setting?.value;\n    },\n    async set (key, value, updatedBy) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.upsert({\n            where: {\n                key\n            },\n            update: {\n                value\n            },\n            create: {\n                key,\n                value\n            }\n        });\n    },\n    async getAll () {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.findMany();\n    }\n};\n// System Logs\nconst systemLogDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.systemLog.create({\n            data: {\n                action: data.action,\n                userId: data.userId,\n                adminId: data.adminId,\n                details: data.details ? JSON.stringify(data.details) : null,\n                ipAddress: data.ipAddress,\n                userAgent: data.userAgent\n            }\n        });\n    }\n};\n// Wallet Balance Database Operations\nconst walletBalanceDb = {\n    async getOrCreate (userId) {\n        let walletBalance = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.findUnique({\n            where: {\n                userId\n            }\n        });\n        if (!walletBalance) {\n            walletBalance = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.create({\n                data: {\n                    userId,\n                    availableBalance: 0,\n                    pendingBalance: 0,\n                    totalDeposits: 0,\n                    totalWithdrawals: 0,\n                    totalEarnings: 0\n                }\n            });\n        }\n        return walletBalance;\n    },\n    async updateBalance (userId, updates) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.update({\n            where: {\n                userId\n            },\n            data: {\n                ...updates,\n                lastUpdated: new Date()\n            }\n        });\n    },\n    async addDeposit (userId, amount) {\n        const wallet = await this.getOrCreate(userId);\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.update({\n            where: {\n                userId\n            },\n            data: {\n                availableBalance: wallet.availableBalance + amount,\n                totalDeposits: wallet.totalDeposits + amount,\n                lastUpdated: new Date()\n            }\n        });\n    },\n    async addEarnings (userId, amount) {\n        const wallet = await this.getOrCreate(userId);\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.update({\n            where: {\n                userId\n            },\n            data: {\n                availableBalance: wallet.availableBalance + amount,\n                totalEarnings: wallet.totalEarnings + amount,\n                lastUpdated: new Date()\n            }\n        });\n    },\n    async deductWithdrawal (userId, amount) {\n        const wallet = await this.getOrCreate(userId);\n        if (wallet.availableBalance < amount) {\n            throw new Error('Insufficient balance');\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.update({\n            where: {\n                userId\n            },\n            data: {\n                availableBalance: wallet.availableBalance - amount,\n                totalWithdrawals: wallet.totalWithdrawals + amount,\n                lastUpdated: new Date()\n            }\n        });\n    },\n    async findByUserId (userId) {\n        return await this.getOrCreate(userId);\n    }\n};\n// Deposit Transaction Database Operations\nconst depositTransactionDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.create({\n            data: {\n                userId: data.userId,\n                transactionId: data.transactionId,\n                amount: data.amount,\n                usdtAmount: data.usdtAmount,\n                tronAddress: data.tronAddress,\n                senderAddress: data.senderAddress,\n                blockNumber: data.blockNumber,\n                blockTimestamp: data.blockTimestamp,\n                confirmations: data.confirmations || 0,\n                status: 'PENDING'\n            }\n        });\n    },\n    async findByTransactionId (transactionId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.findUnique({\n            where: {\n                transactionId\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    async findByUserId (userId, filters) {\n        const where = {\n            userId\n        };\n        if (filters?.status) {\n            where.status = filters.status;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.findMany({\n            where,\n            orderBy: {\n                createdAt: 'desc'\n            },\n            take: filters?.limit || 50,\n            skip: filters?.offset,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    async findAll (filters) {\n        const where = {};\n        if (filters?.status) {\n            where.status = filters.status;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.findMany({\n            where,\n            orderBy: {\n                createdAt: 'desc'\n            },\n            take: filters?.limit || 100,\n            skip: filters?.offset,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    async updateStatus (transactionId, status, updates) {\n        const updateData = {\n            status\n        };\n        if (updates?.verifiedAt) updateData.verifiedAt = updates.verifiedAt;\n        if (updates?.processedAt) updateData.processedAt = updates.processedAt;\n        if (updates?.failureReason) updateData.failureReason = updates.failureReason;\n        if (updates?.confirmations !== undefined) updateData.confirmations = updates.confirmations;\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.update({\n            where: {\n                transactionId\n            },\n            data: updateData\n        });\n    },\n    async markAsCompleted (transactionId) {\n        return await this.updateStatus(transactionId, 'COMPLETED', {\n            processedAt: new Date()\n        });\n    },\n    async markAsFailed (transactionId, reason) {\n        return await this.updateStatus(transactionId, 'FAILED', {\n            failureReason: reason,\n            processedAt: new Date()\n        });\n    },\n    async getPendingDeposits () {\n        return await this.findAll({\n            status: 'PENDING'\n        });\n    },\n    async getPendingVerificationDeposits () {\n        return await this.findAll({\n            status: 'PENDING_VERIFICATION'\n        });\n    },\n    async getWaitingForConfirmationsDeposits () {\n        return await this.findAll({\n            status: 'WAITING_FOR_CONFIRMATIONS'\n        });\n    },\n    async findByStatus (status) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.findMany({\n            where: {\n                status\n            },\n            orderBy: {\n                createdAt: 'desc'\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    async updateConfirmations (transactionId, confirmations) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.update({\n            where: {\n                transactionId\n            },\n            data: {\n                confirmations\n            }\n        });\n    },\n    async getDepositStats () {\n        const stats = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.aggregate({\n            _count: {\n                id: true\n            },\n            _sum: {\n                usdtAmount: true\n            },\n            where: {\n                status: {\n                    in: [\n                        'COMPLETED',\n                        'CONFIRMED'\n                    ]\n                }\n            }\n        });\n        const pendingCount = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.count({\n            where: {\n                status: {\n                    in: [\n                        'PENDING',\n                        'PENDING_VERIFICATION',\n                        'WAITING_FOR_CONFIRMATIONS'\n                    ]\n                }\n            }\n        });\n        return {\n            totalDeposits: stats._count.id || 0,\n            totalAmount: stats._sum.usdtAmount || 0,\n            pendingDeposits: pendingCount\n        };\n    }\n};\n// Support Ticket Database Operations\nconst supportTicketDb = {\n    create: async (data)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.create({\n            data,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            }\n        });\n    },\n    findByUserId: async (userId)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.findMany({\n            where: {\n                userId\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    },\n    findById: async (id)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.findUnique({\n            where: {\n                id\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            }\n        });\n    },\n    findAll: async ()=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.findMany({\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    },\n    updateStatus: async (id, status)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.update({\n            where: {\n                id\n            },\n            data: {\n                status,\n                updatedAt: new Date()\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            }\n        });\n    }\n};\n// Ticket Response Database Operations\nconst ticketResponseDb = {\n    create: async (data)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.ticketResponse.create({\n            data,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    findByTicketId: async (ticketId)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.ticketResponse.findMany({\n            where: {\n                ticketId\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'asc'\n            }\n        });\n    }\n};\n// System Settings Database Operations\nconst systemSettingsDb = {\n    async getSetting (key) {\n        const setting = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.findUnique({\n            where: {\n                key\n            }\n        });\n        return setting?.value || null;\n    },\n    async getSettings (keys) {\n        const settings = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.findMany({\n            where: {\n                key: {\n                    in: keys\n                }\n            }\n        });\n        const result = {};\n        settings.forEach((setting)=>{\n            result[setting.key] = setting.value;\n        });\n        return result;\n    },\n    async updateSettings (settings) {\n        const updates = Object.entries(settings).map(([key, value])=>({\n                key,\n                value: typeof value === 'string' ? value : JSON.stringify(value)\n            }));\n        // Use transaction to update multiple settings\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.$transaction(updates.map(({ key, value })=>_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.upsert({\n                where: {\n                    key\n                },\n                update: {\n                    value\n                },\n                create: {\n                    key,\n                    value\n                }\n            })));\n    },\n    async getEmailSettings () {\n        const settings = await this.getSettings([\n            'smtpHost',\n            'smtpPort',\n            'smtpSecure',\n            'smtpUser',\n            'smtpPassword',\n            'fromName',\n            'fromEmail',\n            'emailEnabled'\n        ]);\n        return {\n            smtpHost: settings.smtpHost,\n            smtpPort: settings.smtpPort ? parseInt(settings.smtpPort) : 587,\n            smtpSecure: settings.smtpSecure === 'true',\n            smtpUser: settings.smtpUser,\n            smtpPassword: settings.smtpPassword,\n            fromName: settings.fromName || 'HashCoreX',\n            fromEmail: settings.fromEmail,\n            emailEnabled: settings.emailEnabled !== 'false'\n        };\n    },\n    async updateEmailSettings (emailSettings) {\n        const settings = {};\n        if (emailSettings.smtpHost !== undefined) settings.smtpHost = emailSettings.smtpHost;\n        if (emailSettings.smtpPort !== undefined) settings.smtpPort = emailSettings.smtpPort.toString();\n        if (emailSettings.smtpSecure !== undefined) settings.smtpSecure = emailSettings.smtpSecure.toString();\n        if (emailSettings.smtpUser !== undefined) settings.smtpUser = emailSettings.smtpUser;\n        if (emailSettings.smtpPassword !== undefined) settings.smtpPassword = emailSettings.smtpPassword;\n        if (emailSettings.fromName !== undefined) settings.fromName = emailSettings.fromName;\n        if (emailSettings.fromEmail !== undefined) settings.fromEmail = emailSettings.fromEmail;\n        if (emailSettings.emailEnabled !== undefined) settings.emailEnabled = emailSettings.emailEnabled.toString();\n        await this.updateSettings(settings);\n    },\n    async getEmailTemplate (name) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.findUnique({\n            where: {\n                name,\n                isActive: true\n            }\n        });\n    }\n};\n// OTP Verification Database Operations\nconst otpDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.oTPVerification.create({\n            data\n        });\n    },\n    async findValid (email, purpose) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.oTPVerification.findFirst({\n            where: {\n                email,\n                purpose,\n                verified: false,\n                expiresAt: {\n                    gt: new Date()\n                }\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    },\n    async verify (id) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.oTPVerification.update({\n            where: {\n                id\n            },\n            data: {\n                verified: true\n            }\n        });\n    },\n    async findVerified (email, purpose) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.oTPVerification.findFirst({\n            where: {\n                email,\n                purpose,\n                verified: true,\n                expiresAt: {\n                    gt: new Date()\n                }\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    },\n    async cleanup () {\n        // Remove expired OTPs\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.oTPVerification.deleteMany({\n            where: {\n                expiresAt: {\n                    lt: new Date()\n                }\n            }\n        });\n    }\n};\n// Email Template Database Operations\nconst emailTemplateDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.create({\n            data\n        });\n    },\n    async findAll () {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.findMany({\n            orderBy: {\n                name: 'asc'\n            }\n        });\n    },\n    async findByName (name) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.findUnique({\n            where: {\n                name\n            }\n        });\n    },\n    async update (name, data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.update({\n            where: {\n                name\n            },\n            data\n        });\n    },\n    async delete (name) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.delete({\n            where: {\n                name\n            }\n        });\n    }\n};\n// Email Log Database Operations\nconst emailLogDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailLog.create({\n            data\n        });\n    },\n    async updateStatus (id, status, error) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailLog.update({\n            where: {\n                id\n            },\n            data: {\n                status,\n                error,\n                sentAt: status === 'SENT' ? new Date() : undefined\n            }\n        });\n    },\n    async findRecent (limit = 50) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailLog.findMany({\n            take: limit,\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/envValidation.ts":
/*!**********************************!*\
  !*** ./src/lib/envValidation.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkCriticalEnvVars: () => (/* binding */ checkCriticalEnvVars),\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   generateStrongJWTSecret: () => (/* binding */ generateStrongJWTSecret),\n/* harmony export */   getValidatedEnv: () => (/* binding */ getValidatedEnv),\n/* harmony export */   validateEnvironment: () => (/* binding */ validateEnvironment)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n/**\n * Environment Variable Validation\n * Validates all required environment variables on application startup\n */ \n// Environment validation schema\nconst envSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    // Database\n    DATABASE_URL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().url('DATABASE_URL must be a valid PostgreSQL URL'),\n    DIRECT_URL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().url('DIRECT_URL must be a valid PostgreSQL URL'),\n    // JWT Configuration\n    JWT_SECRET: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(32, 'JWT_SECRET must be at least 32 characters long').refine((secret)=>{\n        // Check for strong secret\n        const hasUpperCase = /[A-Z]/.test(secret);\n        const hasLowerCase = /[a-z]/.test(secret);\n        const hasNumbers = /\\d/.test(secret);\n        const hasSpecialChars = /[!@#$%^&*(),.?\":{}|<>]/.test(secret);\n        return hasUpperCase && hasLowerCase && hasNumbers && hasSpecialChars;\n    }, 'JWT_SECRET should contain uppercase, lowercase, numbers, and special characters'),\n    JWT_EXPIRES_IN: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().default('30d'),\n    // Node Environment\n    NODE_ENV: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        'development',\n        'production',\n        'test'\n    ]).default('development'),\n    // Application Configuration\n    NEXT_PUBLIC_APP_URL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().url().optional(),\n    PORT: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().regex(/^\\d+$/).transform(Number).default('3000'),\n    // Email Configuration (optional but validated if provided)\n    SMTP_HOST: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    SMTP_PORT: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().regex(/^\\d+$/).transform(Number).optional(),\n    SMTP_USER: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().email().optional(),\n    SMTP_PASSWORD: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    SMTP_SECURE: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().transform((val)=>val === 'true').optional(),\n    // Tron Network Configuration\n    TRON_NETWORK: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        'mainnet',\n        'testnet'\n    ]).default('testnet'),\n    TRON_API_KEY: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    USDT_CONTRACT_ADDRESS: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    // File Upload Configuration\n    MAX_FILE_SIZE: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().regex(/^\\d+$/).transform(Number).default('10485760'),\n    UPLOAD_DIR: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().default('./public/uploads'),\n    // Security Configuration\n    BCRYPT_ROUNDS: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().regex(/^\\d+$/).transform(Number).default('12'),\n    SESSION_TIMEOUT: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().regex(/^\\d+$/).transform(Number).default('1800'),\n    // Rate Limiting Configuration\n    RATE_LIMIT_WINDOW_MS: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().regex(/^\\d+$/).transform(Number).default('900000'),\n    RATE_LIMIT_MAX_REQUESTS: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().regex(/^\\d+$/).transform(Number).default('100'),\n    // External API Configuration\n    COINGECKO_API_URL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().url().default('https://api.coingecko.com/api/v3'),\n    // Monitoring and Logging\n    LOG_LEVEL: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        'error',\n        'warn',\n        'info',\n        'debug'\n    ]).default('info'),\n    ENABLE_REQUEST_LOGGING: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().transform((val)=>val === 'true').default('false'),\n    // Feature Flags\n    ENABLE_REGISTRATION: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().transform((val)=>val === 'true').default('true'),\n    ENABLE_KYC: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().transform((val)=>val === 'true').default('true'),\n    ENABLE_WITHDRAWALS: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().transform((val)=>val === 'true').default('true')\n});\n// Conditional validation for email configuration\nconst envSchemaWithConditionals = envSchema.refine((data)=>{\n    // If any SMTP config is provided, all should be provided\n    const smtpFields = [\n        data.SMTP_HOST,\n        data.SMTP_PORT,\n        data.SMTP_USER,\n        data.SMTP_PASSWORD\n    ];\n    const hasAnySmtp = smtpFields.some((field)=>field !== undefined);\n    const hasAllSmtp = smtpFields.every((field)=>field !== undefined);\n    if (hasAnySmtp && !hasAllSmtp) {\n        return false;\n    }\n    return true;\n}, {\n    message: 'If SMTP configuration is provided, all SMTP fields (HOST, PORT, USER, PASSWORD) must be provided'\n});\n// Validate environment variables\nfunction validateEnvironment() {\n    try {\n        const result = envSchemaWithConditionals.safeParse(process.env);\n        if (!result.success) {\n            const errors = result.error.errors.map((err)=>`${err.path.join('.')}: ${err.message}`);\n            return {\n                success: false,\n                errors\n            };\n        }\n        return {\n            success: true,\n            data: result.data\n        };\n    } catch (error) {\n        return {\n            success: false,\n            errors: [\n                'Failed to validate environment variables'\n            ]\n        };\n    }\n}\n// Get validated environment variables\nlet validatedEnv = null;\nfunction getValidatedEnv() {\n    if (!validatedEnv) {\n        const validation = validateEnvironment();\n        if (!validation.success) {\n            console.error('❌ Environment validation failed:');\n            validation.errors?.forEach((error)=>console.error(`  - ${error}`));\n            process.exit(1);\n        }\n        validatedEnv = validation.data;\n        console.log('✅ Environment variables validated successfully');\n    }\n    return validatedEnv;\n}\n// Environment-specific configurations\nconst config = {\n    isDevelopment: ()=>getValidatedEnv().NODE_ENV === 'development',\n    isProduction: ()=>getValidatedEnv().NODE_ENV === 'production',\n    isTest: ()=>getValidatedEnv().NODE_ENV === 'test',\n    database: {\n        url: ()=>getValidatedEnv().DATABASE_URL,\n        directUrl: ()=>getValidatedEnv().DIRECT_URL\n    },\n    jwt: {\n        secret: ()=>getValidatedEnv().JWT_SECRET,\n        expiresIn: ()=>getValidatedEnv().JWT_EXPIRES_IN\n    },\n    server: {\n        port: ()=>getValidatedEnv().PORT,\n        appUrl: ()=>getValidatedEnv().NEXT_PUBLIC_APP_URL\n    },\n    email: {\n        isConfigured: ()=>{\n            const env = getValidatedEnv();\n            return !!(env.SMTP_HOST && env.SMTP_PORT && env.SMTP_USER && env.SMTP_PASSWORD);\n        },\n        host: ()=>getValidatedEnv().SMTP_HOST,\n        port: ()=>getValidatedEnv().SMTP_PORT,\n        user: ()=>getValidatedEnv().SMTP_USER,\n        password: ()=>getValidatedEnv().SMTP_PASSWORD,\n        secure: ()=>getValidatedEnv().SMTP_SECURE\n    },\n    tron: {\n        network: ()=>getValidatedEnv().TRON_NETWORK,\n        apiKey: ()=>getValidatedEnv().TRON_API_KEY,\n        usdtContract: ()=>getValidatedEnv().USDT_CONTRACT_ADDRESS\n    },\n    security: {\n        bcryptRounds: ()=>getValidatedEnv().BCRYPT_ROUNDS,\n        sessionTimeout: ()=>getValidatedEnv().SESSION_TIMEOUT,\n        maxFileSize: ()=>getValidatedEnv().MAX_FILE_SIZE,\n        uploadDir: ()=>getValidatedEnv().UPLOAD_DIR\n    },\n    rateLimit: {\n        windowMs: ()=>getValidatedEnv().RATE_LIMIT_WINDOW_MS,\n        maxRequests: ()=>getValidatedEnv().RATE_LIMIT_MAX_REQUESTS\n    },\n    features: {\n        registrationEnabled: ()=>getValidatedEnv().ENABLE_REGISTRATION,\n        kycEnabled: ()=>getValidatedEnv().ENABLE_KYC,\n        withdrawalsEnabled: ()=>getValidatedEnv().ENABLE_WITHDRAWALS\n    },\n    logging: {\n        level: ()=>getValidatedEnv().LOG_LEVEL,\n        requestLogging: ()=>getValidatedEnv().ENABLE_REQUEST_LOGGING\n    },\n    external: {\n        coingeckoApiUrl: ()=>getValidatedEnv().COINGECKO_API_URL\n    }\n};\n// Validate environment on module load (server-side only)\nif (true) {\n    // Skip validation during build process\n    const isBuilding = process.env.NEXT_PHASE === 'phase-production-build';\n    if (!isBuilding) {\n        // Only validate in server environment\n        const validation = validateEnvironment();\n        if (!validation.success) {\n            console.error('❌ Environment validation failed:');\n            validation.errors?.forEach((error)=>console.error(`  - ${error}`));\n            // In development, show helpful error message\n            if (true) {\n                console.error('\\n💡 To fix these errors:');\n                console.error('1. Check your .env.local file');\n                console.error('2. Ensure all required environment variables are set');\n                console.error('3. Verify JWT_SECRET is at least 32 characters with mixed case, numbers, and special characters');\n                console.error('4. Ensure database URLs are valid PostgreSQL connection strings');\n            }\n            process.exit(1);\n        }\n        console.log('✅ Environment variables validated successfully');\n    }\n}\n// Helper function to check if all critical environment variables are set\nfunction checkCriticalEnvVars() {\n    const critical = [\n        'DATABASE_URL',\n        'DIRECT_URL',\n        'JWT_SECRET'\n    ];\n    const missing = [];\n    for (const key of critical){\n        if (!process.env[key]) {\n            missing.push(key);\n        }\n    }\n    return {\n        valid: missing.length === 0,\n        missing\n    };\n}\n// Helper function to generate a strong JWT secret\nfunction generateStrongJWTSecret() {\n    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()';\n    let result = '';\n    // Ensure at least one of each required character type\n    result += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'[Math.floor(Math.random() * 26)]; // Uppercase\n    result += 'abcdefghijklmnopqrstuvwxyz'[Math.floor(Math.random() * 26)]; // Lowercase\n    result += '0123456789'[Math.floor(Math.random() * 10)]; // Number\n    result += '!@#$%^&*()'[Math.floor(Math.random() * 10)]; // Special char\n    // Fill the rest randomly\n    for(let i = 4; i < 64; i++){\n        result += chars[Math.floor(Math.random() * chars.length)];\n    }\n    // Shuffle the string\n    return result.split('').sort(()=>Math.random() - 0.5).join('');\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (config);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/envValidation.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFFOUMsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsR0FBRztBQUVuRSxJQUFJSSxJQUFxQyxFQUFFSCxnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZHJlYW1cXERlc2t0b3BcXEhhc2hfTWluaW5nc1xcaGFzaGNvcmV4XFxzcmNcXGxpYlxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59O1xuXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KCk7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hO1xuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJwcm9jZXNzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/rateLimiter.ts":
/*!********************************!*\
  !*** ./src/lib/rateLimiter.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RATE_LIMIT_CONFIGS: () => (/* binding */ RATE_LIMIT_CONFIGS),\n/* harmony export */   adminRateLimit: () => (/* binding */ adminRateLimit),\n/* harmony export */   apiRateLimit: () => (/* binding */ apiRateLimit),\n/* harmony export */   checkAccountLockout: () => (/* binding */ checkAccountLockout),\n/* harmony export */   checkRateLimit: () => (/* binding */ checkRateLimit),\n/* harmony export */   cleanupAccountLockouts: () => (/* binding */ cleanupAccountLockouts),\n/* harmony export */   cleanupRateLimitStore: () => (/* binding */ cleanupRateLimitStore),\n/* harmony export */   clearFailedLogins: () => (/* binding */ clearFailedLogins),\n/* harmony export */   createRateLimitMiddleware: () => (/* binding */ createRateLimitMiddleware),\n/* harmony export */   fileUploadRateLimit: () => (/* binding */ fileUploadRateLimit),\n/* harmony export */   financialRateLimit: () => (/* binding */ financialRateLimit),\n/* harmony export */   getRateLimitStats: () => (/* binding */ getRateLimitStats),\n/* harmony export */   loginRateLimit: () => (/* binding */ loginRateLimit),\n/* harmony export */   recordFailedLogin: () => (/* binding */ recordFailedLogin),\n/* harmony export */   registerRateLimit: () => (/* binding */ registerRateLimit),\n/* harmony export */   userSpecificRateLimit: () => (/* binding */ userSpecificRateLimit)\n/* harmony export */ });\n/**\n * Comprehensive Rate Limiting System\n * Implements multiple rate limiting strategies for different endpoints\n */ // Rate limit store (in production, use Redis)\nconst rateLimitStore = new Map();\n// Different rate limit configurations for different endpoints\nconst RATE_LIMIT_CONFIGS = {\n    // Authentication endpoints - strict limits\n    LOGIN: {\n        windowMs: 15 * 60 * 1000,\n        maxRequests: 5,\n        skipSuccessfulRequests: true\n    },\n    REGISTER: {\n        windowMs: 60 * 60 * 1000,\n        maxRequests: 3\n    },\n    PASSWORD_RESET: {\n        windowMs: 60 * 60 * 1000,\n        maxRequests: 3\n    },\n    // API endpoints - moderate limits\n    API_GENERAL: {\n        windowMs: 60 * 1000,\n        maxRequests: 60\n    },\n    // File upload endpoints - strict limits\n    FILE_UPLOAD: {\n        windowMs: 60 * 1000,\n        maxRequests: 5\n    },\n    // Admin endpoints - very strict limits\n    ADMIN: {\n        windowMs: 60 * 1000,\n        maxRequests: 30\n    },\n    // Financial operations - very strict limits\n    FINANCIAL: {\n        windowMs: 60 * 1000,\n        maxRequests: 10\n    }\n};\n// Progressive delay for repeated violations\nconst PROGRESSIVE_DELAYS = [\n    0,\n    1000,\n    5000,\n    15000,\n    60000,\n    300000,\n    900000\n];\n// Generate rate limit key\nfunction generateKey(request, prefix, customKeyGen) {\n    if (customKeyGen) {\n        return `${prefix}:${customKeyGen(request)}`;\n    }\n    // Use IP address as default key\n    const ip = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';\n    return `${prefix}:${ip}`;\n}\n// Check if request should be rate limited\nfunction checkRateLimit(request, config, prefix = 'general') {\n    const key = generateKey(request, prefix, config.keyGenerator);\n    const now = Date.now();\n    // Get or create rate limit entry\n    let entry = rateLimitStore.get(key);\n    // Check if user is currently blocked due to progressive delay\n    if (entry?.blockedUntil && now < entry.blockedUntil) {\n        return {\n            allowed: false,\n            remaining: 0,\n            resetTime: entry.resetTime,\n            retryAfter: Math.ceil((entry.blockedUntil - now) / 1000)\n        };\n    }\n    // Reset window if expired\n    if (!entry || now > entry.resetTime) {\n        entry = {\n            count: 0,\n            resetTime: now + config.windowMs\n        };\n        rateLimitStore.set(key, entry);\n    }\n    // Check if limit exceeded\n    if (entry.count >= config.maxRequests) {\n        // Apply progressive delay\n        const violationCount = Math.min(entry.count - config.maxRequests, PROGRESSIVE_DELAYS.length - 1);\n        const delay = PROGRESSIVE_DELAYS[violationCount];\n        if (delay > 0) {\n            entry.blockedUntil = now + delay;\n            rateLimitStore.set(key, entry);\n        }\n        return {\n            allowed: false,\n            remaining: 0,\n            resetTime: entry.resetTime,\n            retryAfter: delay > 0 ? Math.ceil(delay / 1000) : Math.ceil((entry.resetTime - now) / 1000)\n        };\n    }\n    // Increment counter\n    entry.count++;\n    rateLimitStore.set(key, entry);\n    return {\n        allowed: true,\n        remaining: config.maxRequests - entry.count,\n        resetTime: entry.resetTime\n    };\n}\n// Rate limit middleware factory\nfunction createRateLimitMiddleware(config, prefix) {\n    return (request)=>{\n        return checkRateLimit(request, config, prefix);\n    };\n}\n// Specific rate limiters for common use cases\nconst loginRateLimit = (request)=>checkRateLimit(request, RATE_LIMIT_CONFIGS.LOGIN, 'login');\nconst registerRateLimit = (request)=>checkRateLimit(request, RATE_LIMIT_CONFIGS.REGISTER, 'register');\nconst apiRateLimit = (request)=>checkRateLimit(request, RATE_LIMIT_CONFIGS.API_GENERAL, 'api');\nconst adminRateLimit = (request)=>checkRateLimit(request, RATE_LIMIT_CONFIGS.ADMIN, 'admin');\nconst fileUploadRateLimit = (request)=>checkRateLimit(request, RATE_LIMIT_CONFIGS.FILE_UPLOAD, 'upload');\nconst financialRateLimit = (request)=>checkRateLimit(request, RATE_LIMIT_CONFIGS.FINANCIAL, 'financial');\n// User-specific rate limiting (for authenticated requests)\nconst userSpecificRateLimit = (request, userId, config, prefix)=>{\n    const customConfig = {\n        ...config,\n        keyGenerator: ()=>userId\n    };\n    return checkRateLimit(request, customConfig, prefix);\n};\n// Clean up expired entries (should be called periodically)\nfunction cleanupRateLimitStore() {\n    const now = Date.now();\n    for (const [key, entry] of rateLimitStore.entries()){\n        if (now > entry.resetTime && (!entry.blockedUntil || now > entry.blockedUntil)) {\n            rateLimitStore.delete(key);\n        }\n    }\n}\n// Get rate limit status for monitoring\nfunction getRateLimitStats() {\n    const now = Date.now();\n    let activeBlocks = 0;\n    for (const entry of rateLimitStore.values()){\n        if (entry.blockedUntil && now < entry.blockedUntil) {\n            activeBlocks++;\n        }\n    }\n    return {\n        totalKeys: rateLimitStore.size,\n        activeBlocks\n    };\n}\n// Account lockout functionality\nconst ACCOUNT_LOCKOUT_STORE = new Map();\nfunction checkAccountLockout(email) {\n    const entry = ACCOUNT_LOCKOUT_STORE.get(email);\n    const now = Date.now();\n    if (!entry) {\n        return {\n            locked: false\n        };\n    }\n    if (entry.lockedUntil && now < entry.lockedUntil) {\n        return {\n            locked: true,\n            remainingTime: Math.ceil((entry.lockedUntil - now) / 1000)\n        };\n    }\n    return {\n        locked: false\n    };\n}\nfunction recordFailedLogin(email) {\n    const now = Date.now();\n    const entry = ACCOUNT_LOCKOUT_STORE.get(email) || {\n        attempts: 0,\n        lastAttempt: 0\n    };\n    // Reset attempts if last attempt was more than 1 hour ago\n    if (now - entry.lastAttempt > 60 * 60 * 1000) {\n        entry.attempts = 0;\n    }\n    entry.attempts++;\n    entry.lastAttempt = now;\n    // Lock account after 5 failed attempts\n    if (entry.attempts >= 5) {\n        // Progressive lockout: 15 min, 30 min, 1 hour, 2 hours, 4 hours\n        const lockoutDurations = [\n            15,\n            30,\n            60,\n            120,\n            240\n        ]; // minutes\n        const lockoutIndex = Math.min(Math.floor(entry.attempts / 5) - 1, lockoutDurations.length - 1);\n        const lockoutMinutes = lockoutDurations[lockoutIndex];\n        entry.lockedUntil = now + lockoutMinutes * 60 * 1000;\n    }\n    ACCOUNT_LOCKOUT_STORE.set(email, entry);\n}\nfunction clearFailedLogins(email) {\n    ACCOUNT_LOCKOUT_STORE.delete(email);\n}\n// Cleanup function for account lockouts\nfunction cleanupAccountLockouts() {\n    const now = Date.now();\n    for (const [email, entry] of ACCOUNT_LOCKOUT_STORE.entries()){\n        if (entry.lockedUntil && now > entry.lockedUntil) {\n            // Reset attempts after lockout expires\n            entry.attempts = 0;\n            entry.lockedUntil = undefined;\n            ACCOUNT_LOCKOUT_STORE.set(email, entry);\n        }\n    }\n}\n// Start cleanup interval (run every 5 minutes)\nif (true) {\n    setInterval(()=>{\n        cleanupRateLimitStore();\n        cleanupAccountLockouts();\n    }, 5 * 60 * 1000);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/rateLimiter.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/secureErrorHandler.ts":
/*!***************************************!*\
  !*** ./src/lib/secureErrorHandler.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorSeverity: () => (/* binding */ ErrorSeverity),\n/* harmony export */   ErrorType: () => (/* binding */ ErrorType),\n/* harmony export */   createAuthError: () => (/* binding */ createAuthError),\n/* harmony export */   createAuthzError: () => (/* binding */ createAuthzError),\n/* harmony export */   createRateLimitError: () => (/* binding */ createRateLimitError),\n/* harmony export */   createSecureError: () => (/* binding */ createSecureError),\n/* harmony export */   createSecureResponse: () => (/* binding */ createSecureResponse),\n/* harmony export */   createValidationError: () => (/* binding */ createValidationError),\n/* harmony export */   handleSecureError: () => (/* binding */ handleSecureError),\n/* harmony export */   withSecureErrorHandling: () => (/* binding */ withSecureErrorHandling)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./database */ \"(rsc)/./src/lib/database.ts\");\n/**\n * Secure Error Handling System\n * Prevents information leakage while maintaining proper error logging\n */ \n\n// Error types for classification\nvar ErrorType = /*#__PURE__*/ function(ErrorType) {\n    ErrorType[\"VALIDATION\"] = \"VALIDATION\";\n    ErrorType[\"AUTHENTICATION\"] = \"AUTHENTICATION\";\n    ErrorType[\"AUTHORIZATION\"] = \"AUTHORIZATION\";\n    ErrorType[\"RATE_LIMIT\"] = \"RATE_LIMIT\";\n    ErrorType[\"DATABASE\"] = \"DATABASE\";\n    ErrorType[\"EXTERNAL_API\"] = \"EXTERNAL_API\";\n    ErrorType[\"FILE_UPLOAD\"] = \"FILE_UPLOAD\";\n    ErrorType[\"BUSINESS_LOGIC\"] = \"BUSINESS_LOGIC\";\n    ErrorType[\"SYSTEM\"] = \"SYSTEM\";\n    ErrorType[\"UNKNOWN\"] = \"UNKNOWN\";\n    return ErrorType;\n}({});\n// Error severity levels\nvar ErrorSeverity = /*#__PURE__*/ function(ErrorSeverity) {\n    ErrorSeverity[\"LOW\"] = \"LOW\";\n    ErrorSeverity[\"MEDIUM\"] = \"MEDIUM\";\n    ErrorSeverity[\"HIGH\"] = \"HIGH\";\n    ErrorSeverity[\"CRITICAL\"] = \"CRITICAL\";\n    return ErrorSeverity;\n}({});\n// Predefined secure error responses\nconst SECURE_ERROR_MESSAGES = {\n    [\"VALIDATION\"]: 'Invalid input provided',\n    [\"AUTHENTICATION\"]: 'Authentication failed',\n    [\"AUTHORIZATION\"]: 'Access denied',\n    [\"RATE_LIMIT\"]: 'Too many requests',\n    [\"DATABASE\"]: 'Service temporarily unavailable',\n    [\"EXTERNAL_API\"]: 'External service error',\n    [\"FILE_UPLOAD\"]: 'File upload failed',\n    [\"BUSINESS_LOGIC\"]: 'Operation failed',\n    [\"SYSTEM\"]: 'Internal server error',\n    [\"UNKNOWN\"]: 'An unexpected error occurred'\n};\n// Status codes for different error types\nconst ERROR_STATUS_CODES = {\n    [\"VALIDATION\"]: 400,\n    [\"AUTHENTICATION\"]: 401,\n    [\"AUTHORIZATION\"]: 403,\n    [\"RATE_LIMIT\"]: 429,\n    [\"DATABASE\"]: 503,\n    [\"EXTERNAL_API\"]: 502,\n    [\"FILE_UPLOAD\"]: 400,\n    [\"BUSINESS_LOGIC\"]: 422,\n    [\"SYSTEM\"]: 500,\n    [\"UNKNOWN\"]: 500\n};\n// Create a secure error\nfunction createSecureError(type, severity, message, userMessage, details) {\n    return {\n        type,\n        severity,\n        message,\n        userMessage: userMessage || SECURE_ERROR_MESSAGES[type],\n        statusCode: ERROR_STATUS_CODES[type],\n        details: sanitizeErrorDetails(details),\n        shouldLog: severity !== \"LOW\",\n        shouldAlert: severity === \"CRITICAL\"\n    };\n}\n// Sanitize error details to prevent information leakage\nfunction sanitizeErrorDetails(details) {\n    if (!details) return undefined;\n    const sanitized = {};\n    for (const [key, value] of Object.entries(details)){\n        // Skip sensitive fields\n        if (key.toLowerCase().includes('password') || key.toLowerCase().includes('secret') || key.toLowerCase().includes('token') || key.toLowerCase().includes('key')) {\n            continue;\n        }\n        // Sanitize string values\n        if (typeof value === 'string') {\n            sanitized[key] = value.substring(0, 1000); // Limit length\n        } else if (typeof value === 'number' || typeof value === 'boolean') {\n            sanitized[key] = value;\n        } else if (Array.isArray(value)) {\n            sanitized[key] = value.slice(0, 10); // Limit array size\n        } else if (typeof value === 'object' && value !== null) {\n            sanitized[key] = '[Object]'; // Don't expose object details\n        }\n    }\n    return sanitized;\n}\n// Handle and log errors securely\nasync function handleSecureError(error, context) {\n    let secureError;\n    // Convert unknown errors to SecureError\n    if (error instanceof Error) {\n        secureError = classifyError(error);\n    } else if (typeof error === 'object' && error !== null && 'type' in error) {\n        secureError = error;\n    } else {\n        secureError = createSecureError(\"UNKNOWN\", \"MEDIUM\", 'Unknown error occurred', 'An unexpected error occurred');\n    }\n    // Log error if required\n    if (secureError.shouldLog) {\n        await logSecureError(secureError, context);\n    }\n    // Send alert for critical errors\n    if (secureError.shouldAlert) {\n        await sendErrorAlert(secureError, context);\n    }\n    return secureError;\n}\n// Classify generic errors into SecureError\nfunction classifyError(error) {\n    const message = error.message.toLowerCase();\n    // Database errors\n    if (message.includes('prisma') || message.includes('database') || message.includes('connection')) {\n        return createSecureError(\"DATABASE\", \"HIGH\", error.message, 'Database service temporarily unavailable');\n    }\n    // Validation errors\n    if (message.includes('validation') || message.includes('invalid') || message.includes('required')) {\n        return createSecureError(\"VALIDATION\", \"LOW\", error.message, 'Invalid input provided');\n    }\n    // Authentication errors\n    if (message.includes('unauthorized') || message.includes('authentication') || message.includes('token')) {\n        return createSecureError(\"AUTHENTICATION\", \"MEDIUM\", error.message, 'Authentication required');\n    }\n    // Authorization errors\n    if (message.includes('forbidden') || message.includes('access denied') || message.includes('permission')) {\n        return createSecureError(\"AUTHORIZATION\", \"MEDIUM\", error.message, 'Access denied');\n    }\n    // File upload errors\n    if (message.includes('file') || message.includes('upload') || message.includes('multipart')) {\n        return createSecureError(\"FILE_UPLOAD\", \"LOW\", error.message, 'File upload failed');\n    }\n    // Default to system error\n    return createSecureError(\"SYSTEM\", \"MEDIUM\", error.message, 'Internal server error');\n}\n// Log error securely\nasync function logSecureError(error, context) {\n    try {\n        await _database__WEBPACK_IMPORTED_MODULE_1__.systemLogDb.create({\n            action: `ERROR_${error.type}`,\n            userId: context?.userId,\n            details: {\n                type: error.type,\n                severity: error.severity,\n                message: error.message,\n                statusCode: error.statusCode,\n                endpoint: context?.endpoint,\n                requestId: context?.requestId,\n                timestamp: new Date().toISOString(),\n                ...error.details\n            },\n            ipAddress: context?.ipAddress || 'unknown',\n            userAgent: context?.userAgent || 'unknown'\n        });\n    } catch (logError) {\n        console.error('Failed to log secure error:', logError);\n    }\n}\n// Send alert for critical errors\nasync function sendErrorAlert(error, context) {\n    // In production, this would send alerts to monitoring systems\n    console.error('CRITICAL ERROR ALERT:', {\n        type: error.type,\n        severity: error.severity,\n        message: error.message,\n        context,\n        timestamp: new Date().toISOString()\n    });\n// TODO: Integrate with alerting systems like PagerDuty, Slack, etc.\n}\n// Create secure API response\nfunction createSecureResponse(error) {\n    const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        success: false,\n        error: error.userMessage,\n        type: error.type,\n        ... true && {\n            debug: {\n                message: error.message,\n                details: error.details\n            }\n        }\n    }, {\n        status: error.statusCode\n    });\n    // Add security headers\n    response.headers.set('X-Content-Type-Options', 'nosniff');\n    response.headers.set('X-Frame-Options', 'DENY');\n    response.headers.set('X-XSS-Protection', '1; mode=block');\n    // Add rate limit headers for rate limit errors\n    if (error.type === \"RATE_LIMIT\" && error.details) {\n        if (error.details.retryAfter) {\n            response.headers.set('Retry-After', error.details.retryAfter.toString());\n        }\n        if (error.details.remaining !== undefined) {\n            response.headers.set('X-RateLimit-Remaining', error.details.remaining.toString());\n        }\n        if (error.details.resetTime) {\n            response.headers.set('X-RateLimit-Reset', new Date(error.details.resetTime).toISOString());\n        }\n    }\n    return response;\n}\n// Middleware wrapper for secure error handling\nfunction withSecureErrorHandling(handler, context) {\n    return async (...args)=>{\n        try {\n            return await handler(...args);\n        } catch (error) {\n            // Extract context from request if available\n            const request = args[0];\n            const requestContext = {\n                endpoint: context?.endpoint || request?.url || 'unknown',\n                ipAddress: request?.headers?.get?.('x-forwarded-for') || 'unknown',\n                userAgent: request?.headers?.get?.('user-agent') || 'unknown',\n                requestId: request?.headers?.get?.('x-request-id') || undefined\n            };\n            const secureError = await handleSecureError(error, requestContext);\n            return createSecureResponse(secureError);\n        }\n    };\n}\n// Validation error helper\nfunction createValidationError(message, details) {\n    return createSecureError(\"VALIDATION\", \"LOW\", message, message, details);\n}\n// Rate limit error helper\nfunction createRateLimitError(retryAfter, remaining, resetTime) {\n    return createSecureError(\"RATE_LIMIT\", \"MEDIUM\", 'Rate limit exceeded', 'Too many requests. Please try again later.', {\n        retryAfter,\n        remaining,\n        resetTime\n    });\n}\n// Authentication error helper\nfunction createAuthError(message) {\n    return createSecureError(\"AUTHENTICATION\", \"MEDIUM\", message || 'Authentication failed', 'Authentication required');\n}\n// Authorization error helper\nfunction createAuthzError(message) {\n    return createSecureError(\"AUTHORIZATION\", \"MEDIUM\", message || 'Access denied', 'You do not have permission to perform this action');\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/secureErrorHandler.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateROI: () => (/* binding */ calculateROI),\n/* harmony export */   calculateTHSPrice: () => (/* binding */ calculateTHSPrice),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   copyToClipboard: () => (/* binding */ copyToClipboard),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDateTime: () => (/* binding */ formatDateTime),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatTHS: () => (/* binding */ formatTHS),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   getTimeUntilBinaryPayout: () => (/* binding */ getTimeUntilBinaryPayout),\n/* harmony export */   getTimeUntilNextPayout: () => (/* binding */ getTimeUntilNextPayout),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   truncateText: () => (/* binding */ truncateText),\n/* harmony export */   validateEmail: () => (/* binding */ validateEmail),\n/* harmony export */   validatePassword: () => (/* binding */ validatePassword)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(rsc)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(rsc)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatCurrency(amount, currency = 'USD') {\n    return new Intl.NumberFormat('en-US', {\n        style: 'currency',\n        currency,\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n    }).format(amount);\n}\nfunction formatNumber(num, decimals = 2) {\n    return new Intl.NumberFormat('en-US', {\n        minimumFractionDigits: decimals,\n        maximumFractionDigits: decimals\n    }).format(num);\n}\nfunction formatDate(date) {\n    const d = typeof date === 'string' ? new Date(date) : date;\n    return new Intl.DateTimeFormat('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n    }).format(d);\n}\nfunction formatDateTime(date) {\n    const d = typeof date === 'string' ? new Date(date) : date;\n    return new Intl.DateTimeFormat('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit'\n    }).format(d);\n}\nfunction truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength) + '...';\n}\nfunction generateId() {\n    // Use crypto.randomUUID if available (modern browsers), fallback to timestamp + random\n    if (typeof crypto !== 'undefined' && crypto.randomUUID) {\n        return crypto.randomUUID().replace(/-/g, '').substring(0, 9);\n    }\n    // Fallback for older browsers or server-side\n    const timestamp = Date.now().toString(36);\n    const randomPart = Math.random().toString(36).substr(2, 5);\n    return (timestamp + randomPart).substr(0, 9);\n}\nfunction sleep(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\nfunction debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\nfunction throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\nfunction copyToClipboard(text) {\n    if (navigator.clipboard) {\n        return navigator.clipboard.writeText(text);\n    }\n    // Fallback for older browsers\n    const textArea = document.createElement('textarea');\n    textArea.value = text;\n    document.body.appendChild(textArea);\n    textArea.focus();\n    textArea.select();\n    try {\n        document.execCommand('copy');\n        return Promise.resolve();\n    } catch (err) {\n        return Promise.reject(err);\n    } finally{\n        document.body.removeChild(textArea);\n    }\n}\nfunction validateEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\nfunction validatePassword(password) {\n    const errors = [];\n    const checks = [];\n    // Check length\n    const lengthValid = password.length >= 8;\n    checks.push({\n        valid: lengthValid,\n        message: 'At least 8 characters long'\n    });\n    if (!lengthValid) {\n        errors.push('Password must be at least 8 characters long');\n    }\n    // Check uppercase\n    const uppercaseValid = /[A-Z]/.test(password);\n    checks.push({\n        valid: uppercaseValid,\n        message: 'At least one uppercase letter'\n    });\n    if (!uppercaseValid) {\n        errors.push('Password must contain at least one uppercase letter');\n    }\n    // Check lowercase\n    const lowercaseValid = /[a-z]/.test(password);\n    checks.push({\n        valid: lowercaseValid,\n        message: 'At least one lowercase letter'\n    });\n    if (!lowercaseValid) {\n        errors.push('Password must contain at least one lowercase letter');\n    }\n    // Check number\n    const numberValid = /\\d/.test(password);\n    checks.push({\n        valid: numberValid,\n        message: 'At least one number'\n    });\n    if (!numberValid) {\n        errors.push('Password must contain at least one number');\n    }\n    // Check special character\n    const specialValid = /[!@#$%^&*(),.?\":{}|<>]/.test(password);\n    checks.push({\n        valid: specialValid,\n        message: 'At least one special character'\n    });\n    if (!specialValid) {\n        errors.push('Password must contain at least one special character');\n    }\n    return {\n        isValid: errors.length === 0,\n        errors,\n        checks\n    };\n}\nfunction calculateROI(investment, dailyRate, days) {\n    return investment * (dailyRate / 100) * days;\n}\nfunction calculateTHSPrice(ths, pricePerTHS) {\n    return ths * pricePerTHS;\n}\nfunction formatTHS(ths) {\n    if (ths >= 1000) {\n        return `${(ths / 1000).toFixed(1)}K TH/s`;\n    }\n    return `${ths.toFixed(2)} TH/s`;\n}\nfunction getTimeUntilNextPayout() {\n    const now = new Date();\n    const nextSaturday = new Date();\n    // Set to next Saturday at 15:00 UTC\n    nextSaturday.setUTCDate(now.getUTCDate() + (6 - now.getUTCDay()));\n    nextSaturday.setUTCHours(15, 0, 0, 0);\n    // If it's already past Saturday 15:00, move to next week\n    if (now > nextSaturday) {\n        nextSaturday.setUTCDate(nextSaturday.getUTCDate() + 7);\n    }\n    const diff = nextSaturday.getTime() - now.getTime();\n    const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n    const hours = Math.floor(diff % (1000 * 60 * 60 * 24) / (1000 * 60 * 60));\n    const minutes = Math.floor(diff % (1000 * 60 * 60) / (1000 * 60));\n    const seconds = Math.floor(diff % (1000 * 60) / 1000);\n    return {\n        days,\n        hours,\n        minutes,\n        seconds\n    };\n}\nfunction getTimeUntilBinaryPayout() {\n    const now = new Date();\n    const nextSaturday = new Date();\n    // Set to next Saturday at 15:00 UTC\n    nextSaturday.setUTCDate(now.getUTCDate() + (6 - now.getUTCDay()));\n    nextSaturday.setUTCHours(15, 0, 0, 0);\n    // If it's already past Saturday 15:00, move to next week\n    if (now > nextSaturday) {\n        nextSaturday.setUTCDate(nextSaturday.getUTCDate() + 7);\n    }\n    const diff = nextSaturday.getTime() - now.getTime();\n    const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n    const hours = Math.floor(diff % (1000 * 60 * 60 * 24) / (1000 * 60 * 60));\n    const minutes = Math.floor(diff % (1000 * 60 * 60) / (1000 * 60));\n    const seconds = Math.floor(diff % (1000 * 60) / 1000);\n    return {\n        days,\n        hours,\n        minutes,\n        seconds\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/zod","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time","vendor-chunks/tailwind-merge","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fregister%2Froute&page=%2Fapi%2Fauth%2Fregister%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fregister%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();